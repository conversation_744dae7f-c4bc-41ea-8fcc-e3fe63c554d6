"""
Station instance service for MeOS Python SDK.

This module provides station instance operations.
Tag: 站点实例信息
"""

import logging
from typing import Any, Dict, Optional

from .base_service import BaseService
from ..models.instances import StationInstanceResponse
from ..models.common import PaginationRequest

logger = logging.getLogger(__name__)


class StationInstanceService(BaseService):
    """Station instance service for MeOS API - 站点实例信息."""
    
    def get_station_info(self, data_code: Optional[str] = None) -> StationInstanceResponse:
        """
        获取站点实例详细信息.
        
        Args:
            data_code: 站点数据编码
            
        Returns:
            完整的API响应，包含 {code, msg, data} 结构
            
        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=data_code)
        
        response = self._make_request(
            method="GET",
            url="/p/dt/v1/ins/station/info",
            response_model=StationInstanceResponse,
            params=params,
        )
        
        return response
    
    async def get_station_info_async(self, data_code: Optional[str] = None) -> StationInstanceResponse:
        """
        获取站点实例详细信息 (异步).
        
        Args:
            data_code: 站点数据编码
            
        Returns:
            完整的API响应，包含 {code, msg, data} 结构
            
        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=data_code)
        
        response = await self._make_async_request(
            method="GET",
            url="/p/dt/v1/ins/station/info",
            response_model=StationInstanceResponse,
            params=params,
        )
        
        return response
