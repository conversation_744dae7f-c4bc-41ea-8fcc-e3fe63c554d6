"""
Tests for MeOS client classes.
"""

import pytest
from unittest.mock import Mock, patch

from meos import MeOSClient, AsyncMeOSClient
from meos.config import ClientConfig
from meos.exceptions import MeOSConfigurationError


class TestMeOSClient:
    """Test cases for MeOSClient."""
    
    def test_client_initialization(self):
        """Test client initialization with basic parameters."""
        client = MeOSClient(
            base_url="https://test.meos.com",
            app_id="test_app",
            app_secret="test_secret"
        )
        
        assert client.config.base_url == "https://test.meos.com"
        assert client.config.app_id == "test_app"
        assert client.config.app_secret == "test_secret"
        
        # Test service properties
        assert hasattr(client, 'auth')
        assert hasattr(client, 'dynamic_data')
        assert hasattr(client, 'project_instances')
    
    def test_client_with_custom_config(self):
        """Test client initialization with custom configuration."""
        config = ClientConfig(
            base_url="https://test.meos.com",
            app_id="test_app",
            app_secret="test_secret",
            verify_ssl=False,
            max_connections=50
        )
        
        client = MeOSClient(
            base_url=config.base_url,
            app_id=config.app_id,
            app_secret=config.app_secret,
            verify_ssl=config.verify_ssl,
            max_connections=config.max_connections
        )
        
        assert client.config.verify_ssl is False
        assert client.config.max_connections == 50
    
    def test_client_context_manager(self):
        """Test client as context manager."""
        with MeOSClient(
            base_url="https://test.meos.com",
            app_id="test_app",
            app_secret="test_secret"
        ) as client:
            assert client is not None
            assert hasattr(client, 'auth')
    
    def test_client_repr(self):
        """Test client string representation."""
        client = MeOSClient(
            base_url="https://test.meos.com",
            app_id="test_app",
            app_secret="test_secret"
        )
        
        repr_str = repr(client)
        assert "MeOSClient" in repr_str
        assert "test.meos.com" in repr_str
        assert "test_app" in repr_str


class TestAsyncMeOSClient:
    """Test cases for AsyncMeOSClient."""
    
    def test_async_client_initialization(self):
        """Test async client initialization."""
        client = AsyncMeOSClient(
            base_url="https://test.meos.com",
            app_id="test_app",
            app_secret="test_secret"
        )
        
        assert client.config.base_url == "https://test.meos.com"
        assert client.config.app_id == "test_app"
        assert client.config.app_secret == "test_secret"
        
        # Test service properties
        assert hasattr(client, 'auth')
        assert hasattr(client, 'dynamic_data')
        assert hasattr(client, 'project_instances')
    
    @pytest.mark.asyncio
    async def test_async_client_context_manager(self):
        """Test async client as context manager."""
        async with AsyncMeOSClient(
            base_url="https://test.meos.com",
            app_id="test_app",
            app_secret="test_secret"
        ) as client:
            assert client is not None
            assert hasattr(client, 'auth')
    
    def test_async_client_repr(self):
        """Test async client string representation."""
        client = AsyncMeOSClient(
            base_url="https://test.meos.com",
            app_id="test_app",
            app_secret="test_secret"
        )
        
        repr_str = repr(client)
        assert "AsyncMeOSClient" in repr_str
        assert "test.meos.com" in repr_str
        assert "test_app" in repr_str


class TestClientConfiguration:
    """Test cases for client configuration."""
    
    def test_invalid_configuration(self):
        """Test client with invalid configuration."""
        with pytest.raises(MeOSConfigurationError):
            MeOSClient(
                base_url="",  # Empty base URL should raise error
                app_id="test_app",
                app_secret="test_secret"
            )
    
    def test_configuration_validation(self):
        """Test configuration parameter validation."""
        # Test negative max_connections
        with pytest.raises(MeOSConfigurationError):
            MeOSClient(
                base_url="https://test.meos.com",
                app_id="test_app",
                app_secret="test_secret",
                max_connections=-1
            )
        
        # Test negative timeout
        with pytest.raises(MeOSConfigurationError):
            MeOSClient(
                base_url="https://test.meos.com",
                app_id="test_app",
                app_secret="test_secret",
                timeout={"connect": -1}
            )


class TestClientServices:
    """Test cases for client service access."""
    
    def test_service_access(self):
        """Test accessing different services through client."""
        client = MeOSClient(
            base_url="https://test.meos.com",
            app_id="test_app",
            app_secret="test_secret"
        )
        
        # Test that services are accessible
        auth_service = client.auth
        assert auth_service is not None
        
        dynamic_data_service = client.dynamic_data
        assert dynamic_data_service is not None
        
        project_service = client.project_instances
        assert project_service is not None
        
        # Test that services have the expected methods
        assert hasattr(auth_service, 'get_token')
        assert hasattr(dynamic_data_service, 'get_dynamic_history')
        assert hasattr(project_service, 'get_project_info')
    
    def test_service_consistency(self):
        """Test that services return the same instance on multiple accesses."""
        client = MeOSClient(
            base_url="https://test.meos.com",
            app_id="test_app",
            app_secret="test_secret"
        )
        
        # Services should return the same instance
        auth1 = client.auth
        auth2 = client.auth
        assert auth1 is auth2
        
        data1 = client.dynamic_data
        data2 = client.dynamic_data
        assert data1 is data2


if __name__ == "__main__":
    pytest.main([__file__])
