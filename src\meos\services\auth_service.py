"""
Authentication service for MeOS Python SDK.

This module provides authentication-related operations.
"""

import logging
from typing import Optional

from ..models.auth import AuthenticationInfo, TokenRequest, TokenResponse
from .base_service import BaseService

logger = logging.getLogger(__name__)


class AuthService(BaseService):
    """Authentication service for MeOS API."""

    def get_token(self, app_id: str, app_secret: str) -> TokenResponse:
        """
        Get authentication token.

        Args:
            app_id: Application ID
            app_secret: Application secret

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If authentication fails
        """
        request_data = TokenRequest(appId=app_id, appSecret=app_secret)
        json_data = self._validate_request_data(request_data)

        response = self._make_request(
            method="POST",
            url="/p/token",
            response_model=TokenResponse,
            json_data=json_data,
        )

        return response

    async def get_token_async(self, app_id: str, app_secret: str) -> TokenResponse:
        """
        Get authentication token asynchronously.

        Args:
            app_id: Application ID
            app_secret: Application secret

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If authentication fails
        """
        request_data = TokenRequest(appId=app_id, appSecret=app_secret)
        json_data = self._validate_request_data(request_data)

        response = await self._make_async_request(
            method="POST",
            url="/p/token",
            response_model=TokenResponse,
            json_data=json_data,
        )

        return response

    def get_current_auth_info(self) -> AuthenticationInfo:
        """
        Get current authentication information.

        Returns:
            Authentication information
        """
        token_manager = self.client.token_manager

        return AuthenticationInfo(
            app_id=token_manager.app_id,
            token=token_manager.token,
            expires_at=token_manager._token_expires_at,
            is_authenticated=token_manager.token is not None,
        )

    def invalidate_token(self) -> None:
        """Invalidate the current authentication token."""
        self.client.token_manager.invalidate_token()
        logger.info("Authentication token invalidated")

    def refresh_token(self) -> str:
        """
        Refresh the authentication token.

        Returns:
            New authentication token

        Raises:
            MeOSAPIError: If token refresh fails
        """
        # Invalidate current token to force refresh
        self.invalidate_token()

        # Get new token
        token_manager = self.client.token_manager
        sync_client = self.client._get_sync_client()

        return token_manager.authenticate_sync(sync_client)

    async def refresh_token_async(self) -> str:
        """
        Refresh the authentication token asynchronously.

        Returns:
            New authentication token

        Raises:
            MeOSAPIError: If token refresh fails
        """
        # Invalidate current token to force refresh
        self.invalidate_token()

        # Get new token
        token_manager = self.client.token_manager
        async_client = self.client._get_async_client()

        return await token_manager.authenticate_async(async_client)

    def is_authenticated(self) -> bool:
        """
        Check if the client is currently authenticated.

        Returns:
            True if authenticated, False otherwise
        """
        return self.client.token_manager.token is not None

    def get_token_info(self) -> Optional[dict]:
        """
        Get information about the current token.

        Returns:
            Token information dictionary or None if not authenticated
        """
        token_manager = self.client.token_manager

        if not token_manager.token:
            return None

        return {
            "token": token_manager.token,
            "expires_at": token_manager._token_expires_at,
            "is_valid": token_manager._is_token_valid(),
            "app_id": token_manager.app_id,
        }
