# MeOS Python SDK

A comprehensive Python SDK for MeOS (Manufacturing Execution and Operations System) API.

## Features

- **Complete API Coverage**: Full support for all MeOS API endpoints organized by functional domains
- **Type Safety**: Complete type hints and Pydantic models for all data structures
- **Async/Sync Support**: Both synchronous and asynchronous client implementations
- **Authentication**: Automatic token management and refresh
- **Error Handling**: Comprehensive exception hierarchy with detailed error information
- **Retry Logic**: Built-in retry mechanisms with exponential backoff
- **Logging**: Structured logging for debugging and monitoring
- **Complete API Response**: Returns full `{code, msg, data}` structure as per MeOS API specification

## Installation

```bash
pip install meos-python-sdk
```

## Quick Start

### Synchronous Client

```python
from meos import MeOSClient

# Initialize the client
client = MeOSClient(
    base_url="https://your-meos-instance.com",
    app_id="your_app_id",
    app_secret="your_app_secret"
)

# Get authentication token
token_response = client.auth.get_token("your_app_id", "your_app_secret")
print(f"Token: {token_response.data.token}")

# Get project information
project_response = client.project_instances.get_project_info("PROJECT_CODE")
if project_response.is_success:
    project = project_response.data
    print(f"Project: {project.pro_name}")

# Get dynamic data history
history_response = client.dynamic_data.get_dynamic_history(
    data_codes=["DATA_CODE_1", "DATA_CODE_2"],
    start_time="2024-01-01 00:00:00",
    end_time="2024-01-01 01:00:00"
)

if history_response.is_success:
    for data_point in history_response.data:
        print(f"{data_point.data_code}: {data_point.value} at {data_point.timestamp}")
```

### Asynchronous Client

```python
import asyncio
from meos import AsyncMeOSClient

async def main():
    async with AsyncMeOSClient(
        base_url="https://your-meos-instance.com",
        app_id="your_app_id",
        app_secret="your_app_secret"
    ) as client:

        # Get real-time data
        realtime_response = await client.dynamic_data.get_dynamic_realtime(
            data_codes=["DATA_CODE_1", "DATA_CODE_2"]
        )

        if realtime_response.is_success:
            for data_point in realtime_response.data:
                print(f"{data_point.data_code}: {data_point.value}")

asyncio.run(main())
```

## API Services

The SDK organizes API endpoints by their functional domains (tags):

### Core Services
- **`auth`** - Authentication operations
- **`dynamic_data`** - Dynamic property data (动态属性数据)
- **`project_instances`** - Project instance information (项目实例信息)
- **`station_instances`** - Station instance information (站点实例信息)
- **`equipment_instances`** - Equipment instance information (设备实例信息)
- **`control`** - Control operations (控制接口)
- **`calculation`** - Calculation framework (计算框架-数据调用)

### Instance Services
- **`unit_instances`** - Unit instance information (单元实例信息)
- **`element_instances`** - Element instance information (元件实例信息)
- **`system_instances`** - System instance information (系统实例信息)
- **`meter_instances`** - Meter instance information (表计实例信息)
- **`pipe_instances`** - Pipe instance information (管路实例信息)

### Production Services
- **`production_energy`** - Production energy information (生产能耗信息)
- **`factory_area`** - Factory area information (厂区信息)
- **`factory_building`** - Factory building information (厂房信息)
- **`workshop`** - Workshop information (车间信息)
- **`production_line`** - Production line information (产线信息)
- **`process`** - Process information (工序信息)
- **`production_equipment`** - Production equipment information (设备信息)

### Other Services
- **`topology`** - Topology relationships (拓扑关系)

## Response Format

All API methods return the complete MeOS API response structure:

```python
{
    "code": 200,        # Status code
    "msg": "success",   # Response message
    "data": {...}       # Actual data
}
```

You can check response success:

```python
response = client.project_instances.get_project_info("PROJECT_CODE")

if response.is_success:
    # Success - use response.data
    project = response.data
    print(f"Project: {project.pro_name}")
else:
    # Error - check response.code and response.msg
    print(f"Error {response.code}: {response.msg}")
```

## Error Handling

The SDK provides comprehensive error handling with specific exception types:

```python
from meos.exceptions import (
    MeOSAPIError,
    MeOSAuthenticationError,
    MeOSConnectionError,
    MeOSTimeoutError
)

try:
    response = client.project_instances.get_project_info("PROJECT_CODE")
    if response.is_success:
        project = response.data
        print(f"Project: {project.pro_name}")
    else:
        print(f"API Error: {response.code} - {response.msg}")

except MeOSAuthenticationError as e:
    print(f"Authentication failed: {e}")

except MeOSConnectionError as e:
    print(f"Connection failed: {e}")

except MeOSTimeoutError as e:
    print(f"Request timed out: {e}")
```

## Configuration

### Basic Configuration

```python
from meos import MeOSClient, ClientConfig

config = ClientConfig(
    base_url="https://your-meos-instance.com",
    app_id="your_app_id",
    app_secret="your_app_secret",
    # Optional configurations
    verify_ssl=True,
    timeout={"connect": 10.0, "read": 30.0},
    retry={"max_retries": 3, "backoff_factor": 0.5}
)

client = MeOSClient(
    base_url=config.base_url,
    app_id=config.app_id,
    app_secret=config.app_secret,
    **config.to_dict()
)
```

## Testing

The SDK includes a comprehensive test suite:

```bash
# Run all tests
pytest

# Run only unit tests
pytest -m "not integration"

# Run with coverage
pytest --cov=meos --cov-report=html

# Run integration tests (requires MeOS instance)
export MEOS_TEST_BASE_URL="https://your-test-instance.com"
export MEOS_TEST_APP_ID="your_test_app_id"
export MEOS_TEST_APP_SECRET="your_test_secret"
pytest -m integration
```

## Development

### Setting up Development Environment

```bash
# Clone the repository
git clone https://github.com/your-org/meos-python-sdk.git
cd meos-python-sdk

# Install in development mode with all dependencies
pip install -e ".[dev,test,docs]"

# Install pre-commit hooks
pre-commit install

# Run tests
pytest

# Run linting
black src tests
isort src tests
flake8 src tests
mypy src
```

## Contributing

We welcome contributions! Please follow these guidelines:

1. **Code Style**: Follow PEP 8 and use the provided linting tools
2. **Type Hints**: All code must include comprehensive type hints
3. **Documentation**: All public APIs must be documented with docstrings
4. **Testing**: All new features must include tests
5. **Backwards Compatibility**: Maintain backwards compatibility when possible

### Submitting Changes

1. Fork the repository
2. Create a feature branch
3. Make your changes with tests
4. Run the full test suite
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

- **Documentation**: See `docs/` directory for detailed guides
- **Issues**: Report bugs and request features via GitHub issues
- **Examples**: Check the `examples/` directory for usage examples

## Changelog

See [CHANGELOG.md](CHANGELOG.md) for a detailed history of changes.