"""
Instance service for MeOS Python SDK.

This module provides instance-related operations for various entity types.
"""

import logging
from typing import List, Optional

from ..models.common import InstanceTreeNode
from ..models.instances import (
    ElementInstance,
    ElementInstanceResponse,
    EquipmentInstance,
    EquipmentInstanceResponse,
    InstanceTreeResponse,
    MeterInstance,
    MeterInstanceResponse,
    PipeInstance,
    PipeInstanceResponse,
    ProjectInstance,
    ProjectInstanceListResponse,
    ProjectInstanceResponse,
    StationInstance,
    StationInstanceResponse,
    SystemInstance,
    SystemInstanceResponse,
    UnitInstance,
    UnitInstanceResponse,
)
from .base_service import BaseService

logger = logging.getLogger(__name__)


class InstanceService(BaseService):
    """Instance service for MeOS API."""

    # Project instance methods
    def get_project_info(self, data_code: Optional[str] = None) -> Optional[ProjectInstance]:
        """
        Get project instance information.

        Args:
            data_code: Project data code

        Returns:
            Project instance or None if not found

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=data_code)

        response = self._make_request(
            method="GET",
            url="/p/dt/v1/ins/project/info",
            response_model=ProjectInstanceResponse,
            params=params,
        )

        return self._extract_data_from_response(response)

    async def get_project_info_async(self, data_code: Optional[str] = None) -> Optional[ProjectInstance]:
        """
        Get project instance information asynchronously.

        Args:
            data_code: Project data code

        Returns:
            Project instance or None if not found

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=data_code)

        response = await self._make_async_request(
            method="GET",
            url="/p/dt/v1/ins/project/info",
            response_model=ProjectInstanceResponse,
            params=params,
        )

        return self._extract_data_from_response(response)

    def list_project_instances(self) -> List[ProjectInstance]:
        """
        List project instances.

        Returns:
            List of project instances

        Raises:
            MeOSAPIError: If the request fails
        """
        response = self._make_request(
            method="GET",
            url="/p/dt/v1/ins/project/ins/list",
            response_model=ProjectInstanceListResponse,
        )

        return self._extract_data_list_from_response(response)

    async def list_project_instances_async(self) -> List[ProjectInstance]:
        """
        List project instances asynchronously.

        Returns:
            List of project instances

        Raises:
            MeOSAPIError: If the request fails
        """
        response = await self._make_async_request(
            method="GET",
            url="/p/dt/v1/ins/project/ins/list",
            response_model=ProjectInstanceListResponse,
        )

        return self._extract_data_list_from_response(response)

    def get_project_tree(self, data_code: str) -> List[InstanceTreeNode]:
        """
        Get project instance tree.

        Args:
            data_code: Project data code

        Returns:
            List of tree nodes

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=data_code)

        response = self._make_request(
            method="GET",
            url="/p/dt/v1/ins/project/ins/tree",
            response_model=InstanceTreeResponse,
            params=params,
        )

        return self._extract_data_list_from_response(response)

    async def get_project_tree_async(self, data_code: str) -> List[InstanceTreeNode]:
        """
        Get project instance tree asynchronously.

        Args:
            data_code: Project data code

        Returns:
            List of tree nodes

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=data_code)

        response = await self._make_async_request(
            method="GET",
            url="/p/dt/v1/ins/project/ins/tree",
            response_model=InstanceTreeResponse,
            params=params,
        )

        return self._extract_data_list_from_response(response)

    # Station instance methods
    def get_station_info(self, data_code: Optional[str] = None) -> Optional[StationInstance]:
        """
        Get station instance information.

        Args:
            data_code: Station data code

        Returns:
            Station instance or None if not found

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=data_code)

        response = self._make_request(
            method="GET",
            url="/p/dt/v1/ins/station/info",
            response_model=StationInstanceResponse,
            params=params,
        )

        return self._extract_data_from_response(response)

    async def get_station_info_async(self, data_code: Optional[str] = None) -> Optional[StationInstance]:
        """
        Get station instance information asynchronously.

        Args:
            data_code: Station data code

        Returns:
            Station instance or None if not found

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=data_code)

        response = await self._make_async_request(
            method="GET",
            url="/p/dt/v1/ins/station/info",
            response_model=StationInstanceResponse,
            params=params,
        )

        return self._extract_data_from_response(response)

    # Equipment instance methods
    def get_equipment_info(self, data_code: Optional[str] = None) -> Optional[EquipmentInstance]:
        """
        Get equipment instance information.

        Args:
            data_code: Equipment data code

        Returns:
            Equipment instance or None if not found

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=data_code)

        response = self._make_request(
            method="GET",
            url="/p/dt/v1/ins/eq/info",
            response_model=EquipmentInstanceResponse,
            params=params,
        )

        return self._extract_data_from_response(response)

    async def get_equipment_info_async(self, data_code: Optional[str] = None) -> Optional[EquipmentInstance]:
        """
        Get equipment instance information asynchronously.

        Args:
            data_code: Equipment data code

        Returns:
            Equipment instance or None if not found

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=data_code)

        response = await self._make_async_request(
            method="GET",
            url="/p/dt/v1/ins/eq/info",
            response_model=EquipmentInstanceResponse,
            params=params,
        )

        return self._extract_data_from_response(response)

    # Unit instance methods
    def get_unit_info(self, data_code: Optional[str] = None) -> Optional[UnitInstance]:
        """
        Get unit instance information.

        Args:
            data_code: Unit data code

        Returns:
            Unit instance or None if not found

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=data_code)

        response = self._make_request(
            method="GET",
            url="/p/dt/v1/ins/unit/info",
            response_model=UnitInstanceResponse,
            params=params,
        )

        return self._extract_data_from_response(response)

    async def get_unit_info_async(self, data_code: Optional[str] = None) -> Optional[UnitInstance]:
        """
        Get unit instance information asynchronously.

        Args:
            data_code: Unit data code

        Returns:
            Unit instance or None if not found

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=data_code)

        response = await self._make_async_request(
            method="GET",
            url="/p/dt/v1/ins/unit/info",
            response_model=UnitInstanceResponse,
            params=params,
        )

        return self._extract_data_from_response(response)

    # Element instance methods
    def get_element_info(self, data_code: Optional[str] = None) -> Optional[ElementInstance]:
        """
        Get element instance information.

        Args:
            data_code: Element data code

        Returns:
            Element instance or None if not found

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=data_code)

        response = self._make_request(
            method="GET",
            url="/p/dt/v1/ins/element/info",
            response_model=ElementInstanceResponse,
            params=params,
        )

        return self._extract_data_from_response(response)

    async def get_element_info_async(self, data_code: Optional[str] = None) -> Optional[ElementInstance]:
        """
        Get element instance information asynchronously.

        Args:
            data_code: Element data code

        Returns:
            Element instance or None if not found

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=data_code)

        response = await self._make_async_request(
            method="GET",
            url="/p/dt/v1/ins/element/info",
            response_model=ElementInstanceResponse,
            params=params,
        )

        return self._extract_data_from_response(response)

    # System instance methods
    def get_system_info(self, data_code: Optional[str] = None) -> Optional[SystemInstance]:
        """
        Get system instance information.

        Args:
            data_code: System data code

        Returns:
            System instance or None if not found

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=data_code)

        response = self._make_request(
            method="GET",
            url="/p/dt/v1/ins/system/info",
            response_model=SystemInstanceResponse,
            params=params,
        )

        return self._extract_data_from_response(response)

    async def get_system_info_async(self, data_code: Optional[str] = None) -> Optional[SystemInstance]:
        """
        Get system instance information asynchronously.

        Args:
            data_code: System data code

        Returns:
            System instance or None if not found

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=data_code)

        response = await self._make_async_request(
            method="GET",
            url="/p/dt/v1/ins/system/info",
            response_model=SystemInstanceResponse,
            params=params,
        )

        return self._extract_data_from_response(response)

    # Meter instance methods
    def get_meter_info(self, data_code: Optional[str] = None) -> Optional[MeterInstance]:
        """
        Get meter instance information.

        Args:
            data_code: Meter data code

        Returns:
            Meter instance or None if not found

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=data_code)

        response = self._make_request(
            method="GET",
            url="/p/dt/v1/ins/meter/info",
            response_model=MeterInstanceResponse,
            params=params,
        )

        return self._extract_data_from_response(response)

    async def get_meter_info_async(self, data_code: Optional[str] = None) -> Optional[MeterInstance]:
        """
        Get meter instance information asynchronously.

        Args:
            data_code: Meter data code

        Returns:
            Meter instance or None if not found

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=data_code)

        response = await self._make_async_request(
            method="GET",
            url="/p/dt/v1/ins/meter/info",
            response_model=MeterInstanceResponse,
            params=params,
        )

        return self._extract_data_from_response(response)

    # Pipe instance methods
    def get_pipe_info(self, data_code: Optional[str] = None) -> Optional[PipeInstance]:
        """
        Get pipe instance information.

        Args:
            data_code: Pipe data code

        Returns:
            Pipe instance or None if not found

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=data_code)

        response = self._make_request(
            method="GET",
            url="/p/dt/v1/ins/pipe/info",
            response_model=PipeInstanceResponse,
            params=params,
        )

        return self._extract_data_from_response(response)

    async def get_pipe_info_async(self, data_code: Optional[str] = None) -> Optional[PipeInstance]:
        """
        Get pipe instance information asynchronously.

        Args:
            data_code: Pipe data code

        Returns:
            Pipe instance or None if not found

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=data_code)

        response = await self._make_async_request(
            method="GET",
            url="/p/dt/v1/ins/pipe/info",
            response_model=PipeInstanceResponse,
            params=params,
        )

        return self._extract_data_from_response(response)
