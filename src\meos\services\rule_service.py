"""
Rule service for MeOS Python SDK.

This module provides rule query operations.
"""

import logging
from typing import Optional

from ..models.base import BaseResponse
from .base_service import BaseService

logger = logging.getLogger(__name__)


class RuleService(BaseService):
    """Rule service for MeOS API - 规则查询."""

    def find_by_sys_name(self, sys_name: str, page: int, size: int) -> BaseResponse:
        """
        根据系统名称模糊查询规则.

        Args:
            sys_name: 系统名称
            page: 页数
            size: 页容量

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(sysName=sys_name, page=page, size=size)

        response = self._make_request(
            method="GET",
            url="/p/rule/energyModule/findBySysName",
            response_model=BaseResponse,
            params=params,
        )

        return response

    def find_by_sys_name_and_station_name(self, sys_name: str, station_name: str, page: str, size: str) -> BaseResponse:
        """
        根据系统名称和站名称模糊查询.

        Args:
            sys_name: 系统名称
            station_name: 站名称
            page: 页码
            size: 页容量

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(sysName=sys_name, stationName=station_name, page=page, size=size)

        response = self._make_request(
            method="GET",
            url="/p/rule/energyModule/findBySysNameAndStationName",
            response_model=BaseResponse,
            params=params,
        )

        return response

    def find_exact_by_sys_name_and_station_name(self, sys_name: str, station_name: str, page: str, size: str) -> BaseResponse:
        """
        根据系统名称和站名称精确查询.

        Args:
            sys_name: 系统名称
            station_name: 站名称
            page: 页码
            size: 页容量

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(sysName=sys_name, stationName=station_name, page=page, size=size)

        response = self._make_request(
            method="GET",
            url="/p/rule/energyModule/findExactBySysNameAndStationName",
            response_model=BaseResponse,
            params=params,
        )

        return response

    def find_by_device_name(self, device_name: str, page: str, size: str) -> BaseResponse:
        """
        根据设备名称模糊查询.

        Args:
            device_name: 设备名称
            page: 页码
            size: 页容量

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(deviceName=device_name, page=page, size=size)

        response = self._make_request(
            method="GET",
            url="/p/rule/energyModule/findByDeviceName",
            response_model=BaseResponse,
            params=params,
        )

        return response

    def find_by_conditions(
        self,
        device_name: str,
        prop_name: str,
        station_name: Optional[str] = None,
        sys_name: Optional[str] = None,
    ) -> BaseResponse:
        """
        综合查询，支持多个字段模糊组合查询.

        Args:
            device_name: 设备模型名称
            prop_name: 属性名称
            station_name: 站模型名称（可选）
            sys_name: 系统模型名称（可选）

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(
            deviceName=device_name,
            propName=prop_name,
            stationName=station_name,
            sysName=sys_name,
        )

        response = self._make_request(
            method="GET",
            url="/p/rule/energyModule/findByConditions",
            response_model=BaseResponse,
            params=params,
        )

        return response

    # Async methods
    async def afind_by_sys_name(self, sys_name: str, page: int, size: int) -> BaseResponse:
        """
        根据系统名称模糊查询规则 (异步).

        Args:
            sys_name: 系统名称
            page: 页数
            size: 页容量

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(sysName=sys_name, page=page, size=size)

        response = await self._amake_request(
            method="GET",
            url="/p/rule/energyModule/findBySysName",
            response_model=BaseResponse,
            params=params,
        )

        return response

    async def afind_by_sys_name_and_station_name(self, sys_name: str, station_name: str, page: str, size: str) -> BaseResponse:
        """
        根据系统名称和站名称模糊查询 (异步).

        Args:
            sys_name: 系统名称
            station_name: 站名称
            page: 页码
            size: 页容量

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(sysName=sys_name, stationName=station_name, page=page, size=size)

        response = await self._amake_request(
            method="GET",
            url="/p/rule/energyModule/findBySysNameAndStationName",
            response_model=BaseResponse,
            params=params,
        )

        return response

    async def afind_exact_by_sys_name_and_station_name(self, sys_name: str, station_name: str, page: str, size: str) -> BaseResponse:
        """
        根据系统名称和站名称精确查询 (异步).

        Args:
            sys_name: 系统名称
            station_name: 站名称
            page: 页码
            size: 页容量

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(sysName=sys_name, stationName=station_name, page=page, size=size)

        response = await self._amake_request(
            method="GET",
            url="/p/rule/energyModule/findExactBySysNameAndStationName",
            response_model=BaseResponse,
            params=params,
        )

        return response

    async def afind_by_device_name(self, device_name: str, page: str, size: str) -> BaseResponse:
        """
        根据设备名称模糊查询 (异步).

        Args:
            device_name: 设备名称
            page: 页码
            size: 页容量

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(deviceName=device_name, page=page, size=size)

        response = await self._amake_request(
            method="GET",
            url="/p/rule/energyModule/findByDeviceName",
            response_model=BaseResponse,
            params=params,
        )

        return response

    async def afind_by_conditions(
        self,
        device_name: str,
        prop_name: str,
        station_name: Optional[str] = None,
        sys_name: Optional[str] = None,
    ) -> BaseResponse:
        """
        综合查询，支持多个字段模糊组合查询 (异步).

        Args:
            device_name: 设备模型名称
            prop_name: 属性名称
            station_name: 站模型名称（可选）
            sys_name: 系统模型名称（可选）

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(
            deviceName=device_name,
            propName=prop_name,
            stationName=station_name,
            sysName=sys_name,
        )

        response = await self._amake_request(
            method="GET",
            url="/p/rule/energyModule/findByConditions",
            response_model=BaseResponse,
            params=params,
        )

        return response
