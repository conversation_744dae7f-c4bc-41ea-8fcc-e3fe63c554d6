"""
Topology service for MeOS Python SDK.

This module provides topology relationship operations.
Tag: 拓扑关系
"""

import logging
from typing import Optional

from ..models.base import BaseResponse
from .base_service import BaseService

logger = logging.getLogger(__name__)


class TopologyService(BaseService):
    """Topology service for MeOS API - 拓扑关系."""

    def get_topology(self, data_code: Optional[str] = None) -> BaseResponse:
        """
        拓扑关系.

        Args:
            data_code: 数据编码

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=data_code)

        response = self._make_request(
            method="GET",
            url="/p/dt/v1/ins/topotaxy",
            response_model=BaseResponse,
            params=params,
        )

        return response

    async def get_topology_async(self, data_code: Optional[str] = None) -> BaseResponse:
        """
        拓扑关系 (异步).

        Args:
            data_code: 数据编码

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=data_code)

        response = await self._make_async_request(
            method="GET",
            url="/p/dt/v1/ins/topotaxy",
            response_model=BaseResponse,
            params=params,
        )

        return response
