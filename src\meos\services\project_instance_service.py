"""
Project instance service for MeOS Python SDK.

This module provides project instance operations.
Tag: 项目实例信息
"""

import logging
from typing import Any, Dict, Optional

from ..models.common import PaginationRequest
from ..models.instances import (
    DynamicPropertyListResponse,
    InstanceTreeResponse,
    ProjectInstanceListResponse,
    ProjectInstanceResponse,
    StaticPropertyListResponse,
)
from .base_service import BaseService

logger = logging.getLogger(__name__)


class ProjectInstanceService(BaseService):
    """Project instance service for MeOS API - 项目实例信息."""

    def get_project_info(self, data_code: Optional[str] = None) -> ProjectInstanceResponse:
        """
        获取项目实例详细信息.

        Args:
            data_code: 项目数据编码

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=data_code)

        response = self._make_request(
            method="GET",
            url="/p/dt/v1/ins/project/info",
            response_model=ProjectInstanceResponse,
            params=params,
        )

        return response

    async def get_project_info_async(self, data_code: Optional[str] = None) -> ProjectInstanceResponse:
        """
        获取项目实例详细信息 (异步).

        Args:
            data_code: 项目数据编码

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=data_code)

        response = await self._make_async_request(
            method="GET",
            url="/p/dt/v1/ins/project/info",
            response_model=ProjectInstanceResponse,
            params=params,
        )

        return response

    def list_project_instances(self) -> ProjectInstanceListResponse:
        """
        查询项目实例列表.

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        response = self._make_request(
            method="GET",
            url="/p/dt/v1/ins/project/ins/list",
            response_model=ProjectInstanceListResponse,
        )

        return response

    async def list_project_instances_async(self) -> ProjectInstanceListResponse:
        """
        查询项目实例列表 (异步).

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        response = await self._make_async_request(
            method="GET",
            url="/p/dt/v1/ins/project/ins/list",
            response_model=ProjectInstanceListResponse,
        )

        return response

    def get_project_tree(self, data_code: str) -> InstanceTreeResponse:
        """
        查询项目实例所有下级（树）.

        Args:
            data_code: 项目数据编码

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=data_code)

        response = self._make_request(
            method="GET",
            url="/p/dt/v1/ins/project/ins/tree",
            response_model=InstanceTreeResponse,
            params=params,
        )

        return response

    async def get_project_tree_async(self, data_code: str) -> InstanceTreeResponse:
        """
        查询项目实例所有下级（树）(异步).

        Args:
            data_code: 项目数据编码

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=data_code)

        response = await self._make_async_request(
            method="GET",
            url="/p/dt/v1/ins/project/ins/tree",
            response_model=InstanceTreeResponse,
            params=params,
        )

        return response

    def get_base_properties(
        self,
        pagination: PaginationRequest,
        data_code: Optional[str] = None,
    ) -> StaticPropertyListResponse:
        """
        获取基础属性列表.

        Args:
            pagination: 分页参数
            data_code: 数据编码

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        request_data: Dict[str, Any] = {
            "current": pagination.page,
            "size": pagination.size,
        }
        if data_code:
            request_data["dataCode"] = data_code

        response = self._make_request(
            method="POST",
            url="/p/dt/v1/ins/project/page/base",
            response_model=StaticPropertyListResponse,
            json_data=request_data,
        )

        return response

    async def get_base_properties_async(
        self,
        pagination: PaginationRequest,
        data_code: Optional[str] = None,
    ) -> StaticPropertyListResponse:
        """
        获取基础属性列表 (异步).

        Args:
            pagination: 分页参数
            data_code: 数据编码

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        request_data: Dict[str, Any] = {
            "current": pagination.page,
            "size": pagination.size,
        }
        if data_code:
            request_data["dataCode"] = data_code

        response = await self._make_async_request(
            method="POST",
            url="/p/dt/v1/ins/project/page/base",
            response_model=StaticPropertyListResponse,
            json_data=request_data,
        )

        return response

    def get_dynamic_properties(
        self,
        pagination: PaginationRequest,
        data_code: Optional[str] = None,
    ) -> DynamicPropertyListResponse:
        """
        获取动态属性列表.

        Args:
            pagination: 分页参数
            data_code: 数据编码

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        request_data: Dict[str, Any] = {
            "current": pagination.page,
            "size": pagination.size,
        }
        if data_code:
            request_data["dataCode"] = data_code

        response = self._make_request(
            method="POST",
            url="/p/dt/v1/ins/project/page/dynamic",
            response_model=DynamicPropertyListResponse,
            json_data=request_data,
        )

        return response

    async def get_dynamic_properties_async(
        self,
        pagination: PaginationRequest,
        data_code: Optional[str] = None,
    ) -> DynamicPropertyListResponse:
        """
        获取动态属性列表 (异步).

        Args:
            pagination: 分页参数
            data_code: 数据编码

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        request_data: Dict[str, Any] = {
            "current": pagination.page,
            "size": pagination.size,
        }
        if data_code:
            request_data["dataCode"] = data_code

        response = await self._make_async_request(
            method="POST",
            url="/p/dt/v1/ins/project/page/dynamic",
            response_model=DynamicPropertyListResponse,
            json_data=request_data,
        )

        return response

    def get_static_properties(
        self,
        pagination: PaginationRequest,
        data_code: Optional[str] = None,
    ) -> StaticPropertyListResponse:
        """
        获取静态属性列表.

        Args:
            pagination: 分页参数
            data_code: 数据编码

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        request_data: Dict[str, Any] = {
            "current": pagination.page,
            "size": pagination.size,
        }
        if data_code:
            request_data["dataCode"] = data_code

        response = self._make_request(
            method="POST",
            url="/p/dt/v1/ins/project/page/static",
            response_model=StaticPropertyListResponse,
            json_data=request_data,
        )

        return response

    async def get_static_properties_async(
        self,
        pagination: PaginationRequest,
        data_code: Optional[str] = None,
    ) -> StaticPropertyListResponse:
        """
        获取静态属性列表 (异步).

        Args:
            pagination: 分页参数
            data_code: 数据编码

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        request_data: Dict[str, Any] = {
            "current": pagination.page,
            "size": pagination.size,
        }
        if data_code:
            request_data["dataCode"] = data_code

        response = await self._make_async_request(
            method="POST",
            url="/p/dt/v1/ins/project/page/static",
            response_model=StaticPropertyListResponse,
            json_data=request_data,
        )

        return response
