"""
Station template service for MeOS Python SDK.

This module provides station template operations.
Tag: 站点模板信息
"""

import logging
from typing import Any, Dict

from ..models.instances import (
    DynamicPropertyListResponse,
    StaticPropertyListResponse,
    StationInstanceResponse,
)
from .base_service import BaseService

logger = logging.getLogger(__name__)


class StationTemplateService(BaseService):
    """Station template service for MeOS API - 站点模板信息."""

    def get_station_model_info(self, data_code: str) -> StationInstanceResponse:
        """
        获取站点模型详细信息.

        Args:
            data_code: 站点编码（必填）

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=data_code)

        response = self._make_request(
            method="GET",
            url="/p/dt/v1/public/model/station/info",
            response_model=StationInstanceResponse,
            params=params,
        )

        return response

    def get_base_properties(self, request: Dict[str, Any]) -> StaticPropertyListResponse:
        """
        获取基础属性列表.

        Args:
            request: 站点模型属性查询参数

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        response = self._make_request(
            method="POST",
            url="/p/dt/v1/public/model/station/page/base",
            response_model=StaticPropertyListResponse,
            json_data=request,
        )

        return response

    def get_dynamic_properties(self, request: Dict[str, Any]) -> DynamicPropertyListResponse:
        """
        获取动态属性列表.

        Args:
            request: 站点模型属性查询参数

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        response = self._make_request(
            method="POST",
            url="/p/dt/v1/public/model/station/page/dynamic",
            response_model=DynamicPropertyListResponse,
            json=request,
        )

        return response

    def get_static_properties(self, request: Dict[str, Any]) -> StaticPropertyListResponse:
        """
        获取静态属性列表.

        Args:
            request: 站点模型属性查询参数

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        response = self._make_request(
            method="POST",
            url="/p/dt/v1/public/model/station/page/static",
            response_model=StaticPropertyListResponse,
            json=request,
        )

        return response

    # Async methods
    async def aget_station_model_info(self, data_code: str) -> StationInstanceResponse:
        """
        获取站点模型详细信息 (异步).

        Args:
            data_code: 站点编码（必填）

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=data_code)

        response = await self._amake_request(
            method="GET",
            url="/p/dt/v1/public/model/station/info",
            response_model=StationInstanceResponse,
            params=params,
        )

        return response

    async def aget_base_properties(self, request: Dict[str, Any]) -> StaticPropertyListResponse:
        """
        获取基础属性列表 (异步).

        Args:
            request: 站点模型属性查询参数

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        response = await self._amake_request(
            method="POST",
            url="/p/dt/v1/public/model/station/page/base",
            response_model=StaticPropertyListResponse,
            json=request,
        )

        return response

    async def aget_dynamic_properties(self, request: Dict[str, Any]) -> DynamicPropertyListResponse:
        """
        获取动态属性列表 (异步).

        Args:
            request: 站点模型属性查询参数

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        response = await self._amake_request(
            method="POST",
            url="/p/dt/v1/public/model/station/page/dynamic",
            response_model=DynamicPropertyListResponse,
            json=request,
        )

        return response

    async def aget_static_properties(self, request: Dict[str, Any]) -> StaticPropertyListResponse:
        """
        获取静态属性列表 (异步).

        Args:
            request: 站点模型属性查询参数

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        response = await self._amake_request(
            method="POST",
            url="/p/dt/v1/public/model/station/page/static",
            response_model=StaticPropertyListResponse,
            json=request,
        )

        return response
