"""
Tests for MeOS service classes.
"""

import pytest
from unittest.mock import Mock, patch
from datetime import datetime

from meos.services.auth_service import AuthService
from meos.services.dynamic_data_service import DynamicDataService
from meos.services.project_instance_service import ProjectInstanceService
from meos.models.auth import TokenResponse, TokenData
from meos.models.dynamic_data import DynamicHistoryRequest, DynamicRealtimeRequest
from meos.models.common import PaginationRequest
from meos.exceptions import MeOSAPIError


class TestAuthService:
    """Test cases for AuthService."""
    
    def test_get_token_success(self, mock_http_client, mock_httpx_response, sample_api_responses):
        """Test successful token retrieval."""
        # Setup
        token_data = TokenData(token="test_token_123")
        response_data = {
            "code": 200,
            "msg": "success",
            "data": {"token": "test_token_123"}
        }
        
        mock_response = mock_httpx_response(200, response_data)
        mock_http_client.request.return_value = mock_response
        
        service = AuthService(mock_http_client)
        
        # Execute
        result = service.get_token("test_app", "test_secret")
        
        # Verify
        assert result.is_success
        assert result.data.token == "test_token_123"
        mock_http_client.request.assert_called_once()
    
    def test_get_token_failure(self, mock_http_client, mock_httpx_response):
        """Test token retrieval failure."""
        # Setup
        response_data = {
            "code": 401,
            "msg": "Invalid credentials",
            "data": None
        }
        
        mock_response = mock_httpx_response(200, response_data)
        mock_http_client.request.return_value = mock_response
        
        service = AuthService(mock_http_client)
        
        # Execute
        result = service.get_token("invalid_app", "invalid_secret")
        
        # Verify
        assert not result.is_success
        assert result.code == 401
        assert "Invalid credentials" in result.msg
    
    @pytest.mark.asyncio
    async def test_get_token_async_success(self, mock_http_client, mock_httpx_response):
        """Test successful async token retrieval."""
        # Setup
        response_data = {
            "code": 200,
            "msg": "success",
            "data": {"token": "test_token_123"}
        }
        
        mock_response = mock_httpx_response(200, response_data)
        mock_http_client.arequest.return_value = mock_response
        
        service = AuthService(mock_http_client)
        
        # Execute
        result = await service.get_token_async("test_app", "test_secret")
        
        # Verify
        assert result.is_success
        assert result.data.token == "test_token_123"
        mock_http_client.arequest.assert_called_once()
    
    def test_get_current_auth_info(self, mock_http_client, mock_token_manager):
        """Test getting current authentication info."""
        # Setup
        mock_http_client.token_manager = mock_token_manager
        service = AuthService(mock_http_client)
        
        # Execute
        auth_info = service.get_current_auth_info()
        
        # Verify
        assert auth_info.app_id == "test_app"
        assert auth_info.token == "test_token_123"
        assert auth_info.is_authenticated is True
    
    def test_invalidate_token(self, mock_http_client, mock_token_manager):
        """Test token invalidation."""
        # Setup
        mock_http_client.token_manager = mock_token_manager
        service = AuthService(mock_http_client)
        
        # Execute
        service.invalidate_token()
        
        # Verify
        mock_token_manager.invalidate_token.assert_called_once()


class TestDynamicDataService:
    """Test cases for DynamicDataService."""
    
    def test_get_dynamic_history_success(self, mock_http_client, mock_httpx_response):
        """Test successful dynamic history data retrieval."""
        # Setup
        response_data = {
            "code": 200,
            "msg": "success",
            "data": [
                {
                    "data_code": "TEMP_001",
                    "value": 25.5,
                    "timestamp": "2024-01-01T00:00:00",
                    "quality": "GOOD"
                }
            ]
        }
        
        mock_response = mock_httpx_response(200, response_data)
        mock_http_client.request.return_value = mock_response
        
        service = DynamicDataService(mock_http_client)
        
        # Execute
        result = service.get_dynamic_history(
            data_codes=["TEMP_001"],
            start_time="2024-01-01 00:00:00",
            end_time="2024-01-01 01:00:00"
        )
        
        # Verify
        assert result.is_success
        assert len(result.data) == 1
        assert result.data[0]["data_code"] == "TEMP_001"
        mock_http_client.request.assert_called_once()
    
    def test_get_dynamic_realtime_success(self, mock_http_client, mock_httpx_response):
        """Test successful dynamic realtime data retrieval."""
        # Setup
        response_data = {
            "code": 200,
            "msg": "success",
            "data": [
                {
                    "data_code": "TEMP_001",
                    "value": 25.5,
                    "quality": "GOOD"
                }
            ]
        }
        
        mock_response = mock_httpx_response(200, response_data)
        mock_http_client.request.return_value = mock_response
        
        service = DynamicDataService(mock_http_client)
        
        # Execute
        result = service.get_dynamic_realtime(data_codes=["TEMP_001"])
        
        # Verify
        assert result.is_success
        assert len(result.data) == 1
        assert result.data[0]["data_code"] == "TEMP_001"
    
    @pytest.mark.asyncio
    async def test_get_dynamic_history_async_success(self, mock_http_client, mock_httpx_response):
        """Test successful async dynamic history data retrieval."""
        # Setup
        response_data = {
            "code": 200,
            "msg": "success",
            "data": [
                {
                    "data_code": "TEMP_001",
                    "value": 25.5,
                    "timestamp": "2024-01-01T00:00:00",
                    "quality": "GOOD"
                }
            ]
        }
        
        mock_response = mock_httpx_response(200, response_data)
        mock_http_client.arequest.return_value = mock_response
        
        service = DynamicDataService(mock_http_client)
        
        # Execute
        result = await service.get_dynamic_history_async(
            data_codes=["TEMP_001"],
            start_time="2024-01-01 00:00:00",
            end_time="2024-01-01 01:00:00"
        )
        
        # Verify
        assert result.is_success
        assert len(result.data) == 1
        mock_http_client.arequest.assert_called_once()
    
    def test_get_dynamic_realtime_with_formula(self, mock_http_client, mock_httpx_response):
        """Test dynamic realtime data with formula calculation."""
        # Setup
        response_data = {
            "code": 200,
            "msg": "success",
            "data": [
                {
                    "data_code": "CALC_001",
                    "value": 100.0,
                    "formula_result": 150.0,
                    "formula_expression": "value * 1.5",
                    "quality": "GOOD"
                }
            ]
        }
        
        mock_response = mock_httpx_response(200, response_data)
        mock_http_client.request.return_value = mock_response
        
        service = DynamicDataService(mock_http_client)
        
        # Execute
        result = service.get_dynamic_realtime_with_formula(
            data_codes=["CALC_001"],
            include_formula=True
        )
        
        # Verify
        assert result.is_success
        assert len(result.data) == 1
        assert result.data[0]["formula_result"] == 150.0


class TestProjectInstanceService:
    """Test cases for ProjectInstanceService."""
    
    def test_get_project_info_success(self, mock_http_client, mock_httpx_response):
        """Test successful project info retrieval."""
        # Setup
        response_data = {
            "code": 200,
            "msg": "success",
            "data": {
                "data_code": "PROJECT_001",
                "pro_name": "Test Project",
                "pro_type": "Manufacturing",
                "description": "Test project"
            }
        }
        
        mock_response = mock_httpx_response(200, response_data)
        mock_http_client.request.return_value = mock_response
        
        service = ProjectInstanceService(mock_http_client)
        
        # Execute
        result = service.get_project_info("PROJECT_001")
        
        # Verify
        assert result.is_success
        assert result.data["data_code"] == "PROJECT_001"
        assert result.data["pro_name"] == "Test Project"
    
    def test_list_project_instances_success(self, mock_http_client, mock_httpx_response):
        """Test successful project list retrieval."""
        # Setup
        response_data = {
            "code": 200,
            "msg": "success",
            "data": [
                {
                    "data_code": "PROJECT_001",
                    "pro_name": "Test Project 1",
                    "pro_type": "Manufacturing"
                },
                {
                    "data_code": "PROJECT_002",
                    "pro_name": "Test Project 2",
                    "pro_type": "Energy"
                }
            ]
        }
        
        mock_response = mock_httpx_response(200, response_data)
        mock_http_client.request.return_value = mock_response
        
        service = ProjectInstanceService(mock_http_client)
        
        # Execute
        result = service.list_project_instances()
        
        # Verify
        assert result.is_success
        assert len(result.data) == 2
        assert result.data[0]["data_code"] == "PROJECT_001"
    
    def test_get_project_tree_success(self, mock_http_client, mock_httpx_response):
        """Test successful project tree retrieval."""
        # Setup
        response_data = {
            "code": 200,
            "msg": "success",
            "data": [
                {
                    "id": "PROJECT_001",
                    "name": "Test Project",
                    "level": 0,
                    "children": [
                        {
                            "id": "STATION_001",
                            "name": "Test Station",
                            "level": 1,
                            "children": []
                        }
                    ]
                }
            ]
        }
        
        mock_response = mock_httpx_response(200, response_data)
        mock_http_client.request.return_value = mock_response
        
        service = ProjectInstanceService(mock_http_client)
        
        # Execute
        result = service.get_project_tree("PROJECT_001")
        
        # Verify
        assert result.is_success
        assert len(result.data) == 1
        assert result.data[0]["id"] == "PROJECT_001"
        assert len(result.data[0]["children"]) == 1
    
    def test_get_base_properties_with_pagination(self, mock_http_client, mock_httpx_response):
        """Test getting base properties with pagination."""
        # Setup
        response_data = {
            "code": 200,
            "msg": "success",
            "data": {
                "records": [
                    {
                        "property_code": "PROP_001",
                        "property_name": "Property 1",
                        "value": "Value 1"
                    }
                ],
                "total": 1,
                "size": 10,
                "current": 1,
                "pages": 1
            }
        }
        
        mock_response = mock_httpx_response(200, response_data)
        mock_http_client.request.return_value = mock_response
        
        service = ProjectInstanceService(mock_http_client)
        pagination = PaginationRequest(page=1, size=10)
        
        # Execute
        result = service.get_base_properties(pagination, "PROJECT_001")
        
        # Verify
        assert result.is_success
        assert result.data["total"] == 1
        assert len(result.data["records"]) == 1


if __name__ == "__main__":
    pytest.main([__file__])
