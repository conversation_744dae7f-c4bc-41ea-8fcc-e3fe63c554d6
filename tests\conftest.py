"""
Pytest configuration and fixtures for MeOS SDK tests.
"""

import pytest
from unittest.mock import Mock, AsyncMock
from typing import Dict, Any

from meos import MeOSClient, AsyncMeOSClient
from meos.config import ClientConfig
from meos.models.auth import TokenResponse, TokenData
from meos.models.instances import ProjectInstanceResponse, ProjectInstance
from meos.models.dynamic_data import DynamicRealtimeResponse, DynamicPropertyData


@pytest.fixture
def mock_config():
    """Mock client configuration."""
    return ClientConfig(
        base_url="https://test.meos.com",
        app_id="test_app",
        app_secret="test_secret",
        verify_ssl=False,
        max_connections=10
    )


@pytest.fixture
def mock_token_response():
    """Mock token response."""
    token_data = TokenData(token="test_token_123")
    return TokenResponse(code=200, msg="success", data=token_data)


@pytest.fixture
def mock_project_instance():
    """Mock project instance."""
    return ProjectInstance(
        data_code="PROJECT_001",
        name="Test Project",
        pro_name="Test Project",
        pro_type="Manufacturing",
        description="Test project description"
    )


@pytest.fixture
def mock_project_response(mock_project_instance):
    """Mock project instance response."""
    return ProjectInstanceResponse(
        code=200,
        msg="success",
        data=mock_project_instance
    )


@pytest.fixture
def mock_dynamic_data():
    """Mock dynamic property data."""
    return [
        DynamicPropertyData(
            data_code="TEMP_001",
            value=25.5,
            unit="°C",
            quality="GOOD"
        ),
        DynamicPropertyData(
            data_code="PRESSURE_001",
            value=1.2,
            unit="bar",
            quality="GOOD"
        )
    ]


@pytest.fixture
def mock_dynamic_response(mock_dynamic_data):
    """Mock dynamic data response."""
    return DynamicRealtimeResponse(
        code=200,
        msg="success",
        data=mock_dynamic_data
    )


@pytest.fixture
def mock_error_response():
    """Mock error response."""
    return {
        "code": 400,
        "msg": "Bad request",
        "data": None
    }


@pytest.fixture
def mock_http_client():
    """Mock HTTP client."""
    client = Mock()
    client.request = Mock()
    client.arequest = AsyncMock()
    client.close = Mock()
    client.aclose = AsyncMock()
    return client


@pytest.fixture
def mock_token_manager():
    """Mock token manager."""
    manager = Mock()
    manager.app_id = "test_app"
    manager.app_secret = "test_secret"
    manager.token = "test_token_123"
    manager._token_expires_at = None
    manager.authenticate_sync = Mock(return_value="test_token_123")
    manager.authenticate_async = AsyncMock(return_value="test_token_123")
    manager.invalidate_token = Mock()
    manager.get_auth_headers = Mock(return_value={"Siact-token": "test_token_123"})
    manager._is_token_valid = Mock(return_value=True)
    return manager


@pytest.fixture
def client_with_mocks(mock_config, mock_http_client, mock_token_manager):
    """Create a client with mocked dependencies."""
    client = MeOSClient(
        base_url=mock_config.base_url,
        app_id=mock_config.app_id,
        app_secret=mock_config.app_secret
    )
    
    # Replace internal components with mocks
    client._http_client = mock_http_client
    client._http_client.token_manager = mock_token_manager
    
    return client


@pytest.fixture
async def async_client_with_mocks(mock_config, mock_http_client, mock_token_manager):
    """Create an async client with mocked dependencies."""
    client = AsyncMeOSClient(
        base_url=mock_config.base_url,
        app_id=mock_config.app_id,
        app_secret=mock_config.app_secret
    )
    
    # Replace internal components with mocks
    client._http_client = mock_http_client
    client._http_client.token_manager = mock_token_manager
    
    return client


@pytest.fixture
def sample_api_responses():
    """Sample API responses for testing."""
    return {
        "success_response": {
            "code": 200,
            "msg": "success",
            "data": {"result": "test_data"}
        },
        "error_response": {
            "code": 400,
            "msg": "Bad request",
            "data": None
        },
        "auth_error_response": {
            "code": 401,
            "msg": "Unauthorized",
            "data": None
        },
        "not_found_response": {
            "code": 404,
            "msg": "Not found",
            "data": None
        },
        "server_error_response": {
            "code": 500,
            "msg": "Internal server error",
            "data": None
        }
    }


@pytest.fixture
def mock_httpx_response():
    """Mock httpx response."""
    def create_response(status_code: int, json_data: Dict[str, Any]):
        response = Mock()
        response.status_code = status_code
        response.json.return_value = json_data
        response.text = str(json_data)
        response.reason_phrase = "OK" if status_code == 200 else "Error"
        return response
    
    return create_response


@pytest.fixture(autouse=True)
def reset_mocks():
    """Reset all mocks after each test."""
    yield
    # This fixture runs after each test to clean up


# Pytest configuration
def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
    config.addinivalue_line(
        "markers", "network: mark test as requiring network access"
    )


# Test data constants
TEST_BASE_URL = "https://test.meos.com"
TEST_APP_ID = "test_app_id"
TEST_APP_SECRET = "test_app_secret"
TEST_PROJECT_CODE = "PROJECT_001"
TEST_DATA_CODES = ["TEMP_001", "PRESSURE_001"]
TEST_TOKEN = "test_token_123456789"


@pytest.fixture
def test_constants():
    """Test constants for use in tests."""
    return {
        "base_url": TEST_BASE_URL,
        "app_id": TEST_APP_ID,
        "app_secret": TEST_APP_SECRET,
        "project_code": TEST_PROJECT_CODE,
        "data_codes": TEST_DATA_CODES,
        "token": TEST_TOKEN
    }
