"""
Message service for MeOS Python SDK.

This module provides message bus operations.
"""

import logging
from typing import Any, Dict, List

from ..models.base import BaseResponse
from .base_service import BaseService

logger = logging.getLogger(__name__)


class MessageService(BaseService):
    """Message service for MeOS API - 消息总线."""

    def send_message(
        self,
        service_id: str,
        service_secret: str,
        topic_id: str,
        messages: List[Dict[str, Any]],
        is_forward: bool = True,
    ) -> BaseResponse:
        """
        发送消息.

        消息总线发送消息接口，需要配合MeOS工具使用。使用前先在消息总线中配置，然后再调用。

        Args:
            service_id: 生产者服务【服务ID】，在消息总线【服务管理】->【生产者服务管理】->【服务ID】中获取
            service_secret: 生产者服务【服务secret】，在消息总线【服务管理】->【生产者服务管理】->【服务secret】中获取
            topic_id: 消息主题调用ID，从消息总线【消息主题管理】->【消息主题管理】->【Tpopic调用ID】
            messages: 消息内容列表，可多条
            is_forward: 是否转发消息，true:消息总线会使用内部机制将消息转发给消费者；false:消息总线只将消息发送到对应的中间件，不做转发，默认true

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails

        Example:
            >>> message_service = MessageService(http_client)
            >>> messages = [
            ...     {
            ...         "is_delay": False,
            ...         "message_id": "83b5a343-f778-415c-9b36-46b5edb3c01f",
            ...         "content": "赵六18"
            ...     }
            ... ]
            >>> response = message_service.send_message(
            ...     service_id="1828735305230323714",
            ...     service_secret="2fd2cdee21a04aa2b474942a5927b013",
            ...     topic_id="9a19079506b148dca5a2176b9d94baa5",
            ...     messages=messages
            ... )
        """
        headers = {
            "service_id": service_id,
            "service_secret": service_secret,
        }

        request_data = {
            "topic_id": topic_id,
            "is_forward": is_forward,
            "messages": messages,
        }

        response = self._make_request(
            method="POST",
            url="/p/mbus/message/send",
            response_model=BaseResponse,
            json_data=request_data,
            headers=headers,
        )

        return response

    # Async methods
    async def asend_message(
        self,
        service_id: str,
        service_secret: str,
        topic_id: str,
        messages: List[Dict[str, Any]],
        is_forward: bool = True,
    ) -> BaseResponse:
        """
        发送消息 (异步).

        消息总线发送消息接口，需要配合MeOS工具使用。使用前先在消息总线中配置，然后再调用。

        Args:
            service_id: 生产者服务【服务ID】，在消息总线【服务管理】->【生产者服务管理】->【服务ID】中获取
            service_secret: 生产者服务【服务secret】，在消息总线【服务管理】->【生产者服务管理】->【服务secret】中获取
            topic_id: 消息主题调用ID，从消息总线【消息主题管理】->【消息主题管理】->【Tpopic调用ID】
            messages: 消息内容列表，可多条
            is_forward: 是否转发消息，true:消息总线会使用内部机制将消息转发给消费者；false:消息总线只将消息发送到对应的中间件，不做转发，默认true

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        headers = {
            "service_id": service_id,
            "service_secret": service_secret,
        }

        request_data = {
            "topic_id": topic_id,
            "is_forward": is_forward,
            "messages": messages,
        }

        response = await self._make_async_request(
            method="POST",
            url="/p/mbus/message/send",
            response_model=BaseResponse,
            json_data=request_data,
            headers=headers,
        )

        return response
