"""
Project template service for MeOS Python SDK.

This module provides project template operations.
Tag: 项目模板信息
"""

import logging
from typing import Any, Dict, Optional

from ..models.instances import (
    DynamicPropertyListResponse,
    InstanceTreeResponse,
    ProjectInstanceResponse,
    StaticPropertyListResponse,
)
from .base_service import BaseService

logger = logging.getLogger(__name__)


class ProjectTemplateService(BaseService):
    """Project template service for MeOS API - 项目模板信息."""

    def get_project_model_info(self, data_code: Optional[str] = None) -> ProjectInstanceResponse:
        """
        获取项目模型详细信息.

        Args:
            data_code: 项目编码

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=data_code)

        response = self._make_request(
            method="GET",
            url="/p/dt/v1/public/model/project/info",
            response_model=ProjectInstanceResponse,
            params=params,
        )

        return response

    def list_project_models(self) -> InstanceTreeResponse:
        """
        查询项目模型列表.

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        response = self._make_request(
            method="GET",
            url="/p/dt/v1/public/model/project/ins/list",
            response_model=InstanceTreeResponse,
        )

        return response

    def get_project_model_tree(self, data_code: str) -> InstanceTreeResponse:
        """
        查询项目模型所有下级（树）.

        Args:
            data_code: 项目编码（必填）

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=data_code)

        response = self._make_request(
            method="GET",
            url="/p/dt/v1/public/model/project/ins/tree",
            response_model=InstanceTreeResponse,
            params=params,
        )

        return response

    def get_base_properties(self, request: Dict[str, Any]) -> StaticPropertyListResponse:
        """
        获取基础属性列表.

        Args:
            request: 项目模型属性查询参数

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        response = self._make_request(
            method="POST",
            url="/p/dt/v1/public/model/project/page/base",
            response_model=StaticPropertyListResponse,
            json_data=request,
        )

        return response

    def get_dynamic_properties(self, request: Dict[str, Any]) -> DynamicPropertyListResponse:
        """
        获取动态属性列表.

        Args:
            request: 项目模型属性查询参数

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        response = self._make_request(
            method="POST",
            url="/p/dt/v1/public/model/project/page/dynamic",
            response_model=DynamicPropertyListResponse,
            json_data=request,
        )

        return response

    def get_static_properties(self, request: Dict[str, Any]) -> StaticPropertyListResponse:
        """
        获取静态属性列表.

        Args:
            request: 项目模型属性查询参数

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        response = self._make_request(
            method="POST",
            url="/p/dt/v1/public/model/project/page/static",
            response_model=StaticPropertyListResponse,
            json_data=request,
        )

        return response

    # Async methods
    async def aget_project_model_info(self, data_code: Optional[str] = None) -> ProjectInstanceResponse:
        """
        获取项目模型详细信息 (异步).

        Args:
            data_code: 项目编码

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=data_code)

        response = await self._make_async_request(
            method="GET",
            url="/p/dt/v1/public/model/project/info",
            response_model=ProjectInstanceResponse,
            params=params,
        )

        return response

    async def alist_project_models(self) -> InstanceTreeResponse:
        """
        查询项目模型列表 (异步).

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        response = await self._make_async_request(
            method="GET",
            url="/p/dt/v1/public/model/project/ins/list",
            response_model=InstanceTreeResponse,
        )

        return response

    async def aget_project_model_tree(self, data_code: str) -> InstanceTreeResponse:
        """
        查询项目模型所有下级（树） (异步).

        Args:
            data_code: 项目编码（必填）

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=data_code)

        response = await self._make_async_request(
            method="GET",
            url="/p/dt/v1/public/model/project/ins/tree",
            response_model=InstanceTreeResponse,
            params=params,
        )

        return response

    async def aget_base_properties(self, request: Dict[str, Any]) -> StaticPropertyListResponse:
        """
        获取基础属性列表 (异步).

        Args:
            request: 项目模型属性查询参数

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        response = await self._make_async_request(
            method="POST",
            url="/p/dt/v1/public/model/project/page/base",
            response_model=StaticPropertyListResponse,
            json_data=request,
        )

        return response

    async def aget_dynamic_properties(self, request: Dict[str, Any]) -> DynamicPropertyListResponse:
        """
        获取动态属性列表 (异步).

        Args:
            request: 项目模型属性查询参数

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        response = await self._make_async_request(
            method="POST",
            url="/p/dt/v1/public/model/project/page/dynamic",
            response_model=DynamicPropertyListResponse,
            json_data=request,
        )

        return response

    async def aget_static_properties(self, request: Dict[str, Any]) -> StaticPropertyListResponse:
        """
        获取静态属性列表 (异步).

        Args:
            request: 项目模型属性查询参数

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        response = await self._make_async_request(
            method="POST",
            url="/p/dt/v1/public/model/project/page/static",
            response_model=StaticPropertyListResponse,
            json_data=request,
        )

        return response
