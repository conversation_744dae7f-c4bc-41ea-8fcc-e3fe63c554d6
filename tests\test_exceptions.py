"""
Tests for MeOS exception classes.
"""

import pytest

from meos.exceptions import (
    MeOSError,
    MeOSAPIError,
    MeOSAuthenticationError,
    MeOSAuthorizationError,
    MeOSNotFoundError,
    MeOSValidationError,
    MeOSConnectionError,
    MeOSTimeoutError,
    MeOSRateLimitError,
    MeOSServerError,
    MeOSConfigurationError,
    create_api_error,
)


class TestMeOSError:
    """Test cases for base MeOSError."""
    
    def test_basic_error(self):
        """Test basic error creation."""
        error = MeOSError("Test error")
        assert str(error) == "Test error"
        assert error.message == "Test error"
        assert error.code is None
        assert error.details == {}
    
    def test_error_with_code(self):
        """Test error with code."""
        error = MeOSError("Test error", code=400)
        assert str(error) == "[400] Test error"
        assert error.code == 400
    
    def test_error_with_details(self):
        """Test error with details."""
        details = {"field": "value", "extra": "info"}
        error = MeOSError("Test error", details=details)
        assert error.details == details
    
    def test_error_repr(self):
        """Test error representation."""
        error = MeOSError("Test error", code=400)
        repr_str = repr(error)
        assert "MeOSError" in repr_str
        assert "Test error" in repr_str
        assert "400" in repr_str


class TestMeOSAPIError:
    """Test cases for MeOSAPIError."""
    
    def test_api_error_creation(self):
        """Test API error creation."""
        response_data = {"code": 400, "msg": "Bad request"}
        error = MeOSAPIError(
            "API error",
            code=400,
            status_code=400,
            response_data=response_data
        )
        
        assert error.message == "API error"
        assert error.code == 400
        assert error.status_code == 400
        assert error.response_data == response_data


class TestSpecificErrors:
    """Test cases for specific error types."""
    
    def test_authentication_error(self):
        """Test authentication error."""
        error = MeOSAuthenticationError()
        assert "Authentication failed" in str(error)
        
        error = MeOSAuthenticationError("Custom auth error")
        assert str(error) == "Custom auth error"
    
    def test_authorization_error(self):
        """Test authorization error."""
        error = MeOSAuthorizationError()
        assert "Access forbidden" in str(error)
    
    def test_not_found_error(self):
        """Test not found error."""
        error = MeOSNotFoundError()
        assert "Resource not found" in str(error)
    
    def test_validation_error(self):
        """Test validation error."""
        error = MeOSValidationError("Invalid field", field="username", value="")
        assert error.field == "username"
        assert error.value == ""
    
    def test_connection_error(self):
        """Test connection error."""
        original_error = ConnectionError("Network error")
        error = MeOSConnectionError("Connection failed", original_error=original_error)
        assert error.original_error == original_error
    
    def test_timeout_error(self):
        """Test timeout error."""
        error = MeOSTimeoutError("Request timed out", timeout=30.0)
        assert error.timeout == 30.0
    
    def test_rate_limit_error(self):
        """Test rate limit error."""
        error = MeOSRateLimitError("Rate limit exceeded", retry_after=60)
        assert error.retry_after == 60
    
    def test_server_error(self):
        """Test server error."""
        error = MeOSServerError("Internal server error", status_code=500)
        assert error.status_code == 500
    
    def test_configuration_error(self):
        """Test configuration error."""
        error = MeOSConfigurationError("Invalid config", parameter="timeout")
        assert error.parameter == "timeout"


class TestCreateAPIError:
    """Test cases for create_api_error function."""
    
    def test_create_authentication_error(self):
        """Test creating authentication error."""
        response_data = {"code": 401, "msg": "Unauthorized"}
        error = create_api_error(401, response_data)
        
        assert isinstance(error, MeOSAuthenticationError)
        assert error.status_code == 401
        assert error.code == 401
        assert "Unauthorized" in error.message
    
    def test_create_authorization_error(self):
        """Test creating authorization error."""
        response_data = {"code": 403, "msg": "Forbidden"}
        error = create_api_error(403, response_data)
        
        assert isinstance(error, MeOSAuthorizationError)
        assert error.status_code == 403
    
    def test_create_not_found_error(self):
        """Test creating not found error."""
        response_data = {"code": 404, "msg": "Not found"}
        error = create_api_error(404, response_data)
        
        assert isinstance(error, MeOSNotFoundError)
        assert error.status_code == 404
    
    def test_create_rate_limit_error(self):
        """Test creating rate limit error."""
        response_data = {"code": 429, "msg": "Too many requests", "Retry-After": "60"}
        error = create_api_error(429, response_data)
        
        assert isinstance(error, MeOSRateLimitError)
        assert error.status_code == 429
        assert error.retry_after == 60
    
    def test_create_server_error(self):
        """Test creating server error."""
        response_data = {"code": 500, "msg": "Internal server error"}
        error = create_api_error(500, response_data)
        
        assert isinstance(error, MeOSServerError)
        assert error.status_code == 500
    
    def test_create_generic_api_error(self):
        """Test creating generic API error."""
        response_data = {"code": 422, "msg": "Unprocessable entity"}
        error = create_api_error(422, response_data)
        
        assert isinstance(error, MeOSAPIError)
        assert not isinstance(error, MeOSAuthenticationError)
        assert error.status_code == 422
    
    def test_create_error_without_response_data(self):
        """Test creating error without response data."""
        error = create_api_error(400)
        
        assert isinstance(error, MeOSAPIError)
        assert error.status_code == 400
        assert "HTTP 400 error" in error.message
    
    def test_create_error_with_custom_message(self):
        """Test creating error with custom message."""
        error = create_api_error(400, message="Custom error message")
        
        assert error.message == "Custom error message"
    
    def test_create_error_invalid_retry_after(self):
        """Test creating rate limit error with invalid retry-after."""
        response_data = {"code": 429, "msg": "Too many requests", "Retry-After": "invalid"}
        error = create_api_error(429, response_data)
        
        assert isinstance(error, MeOSRateLimitError)
        assert error.retry_after is None


if __name__ == "__main__":
    pytest.main([__file__])
