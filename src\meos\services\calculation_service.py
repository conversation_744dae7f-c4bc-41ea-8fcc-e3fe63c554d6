"""
Calculation service for MeOS Python SDK.

This module provides calculation framework operations.
Tag: 计算框架-数据调用
"""

import logging
from typing import Any, Dict

from .base_service import BaseService
from ..models.base import BaseResponse

logger = logging.getLogger(__name__)


class CalculationService(BaseService):
    """Calculation service for MeOS API - 计算框架-数据调用."""
    
    def get_equally_spaced_time_data(
        self,
        request_data: Dict[str, Any],
    ) -> BaseResponse:
        """
        获取历史数据等时间间隔采样数据.
        
        Args:
            request_data: 请求数据
            
        Returns:
            完整的API响应，包含 {code, msg, data} 结构
            
        Raises:
            MeOSAPIError: If the request fails
        """
        response = self._make_request(
            method="POST",
            url="/p/calc/api/history/getEquallySpacedTimeData",
            response_model=BaseResponse,
            json_data=request_data,
        )
        
        return response
    
    async def get_equally_spaced_time_data_async(
        self,
        request_data: Dict[str, Any],
    ) -> BaseResponse:
        """
        获取历史数据等时间间隔采样数据 (异步).
        
        Args:
            request_data: 请求数据
            
        Returns:
            完整的API响应，包含 {code, msg, data} 结构
            
        Raises:
            MeOSAPIError: If the request fails
        """
        response = await self._make_async_request(
            method="POST",
            url="/p/calc/api/history/getEquallySpacedTimeData",
            response_model=BaseResponse,
            json_data=request_data,
        )
        
        return response
    
    def get_time_interval_data(
        self,
        request_data: Dict[str, Any],
    ) -> BaseResponse:
        """
        获取时间区间数据.
        
        Args:
            request_data: 请求数据
            
        Returns:
            完整的API响应，包含 {code, msg, data} 结构
            
        Raises:
            MeOSAPIError: If the request fails
        """
        response = self._make_request(
            method="POST",
            url="/p/calc/api/history/getTimeIntervalData",
            response_model=BaseResponse,
            json_data=request_data,
        )
        
        return response
    
    async def get_time_interval_data_async(
        self,
        request_data: Dict[str, Any],
    ) -> BaseResponse:
        """
        获取时间区间数据 (异步).
        
        Args:
            request_data: 请求数据
            
        Returns:
            完整的API响应，包含 {code, msg, data} 结构
            
        Raises:
            MeOSAPIError: If the request fails
        """
        response = await self._make_async_request(
            method="POST",
            url="/p/calc/api/history/getTimeIntervalData",
            response_model=BaseResponse,
            json_data=request_data,
        )
        
        return response
    
    def get_time_slice_data(
        self,
        request_data: Dict[str, Any],
    ) -> BaseResponse:
        """
        获取指定时间断面数据,最近15分钟最后一包数据.
        
        Args:
            request_data: 请求数据
            
        Returns:
            完整的API响应，包含 {code, msg, data} 结构
            
        Raises:
            MeOSAPIError: If the request fails
        """
        response = self._make_request(
            method="POST",
            url="/p/calc/api/history/getTimeSliceData",
            response_model=BaseResponse,
            json_data=request_data,
        )
        
        return response
    
    async def get_time_slice_data_async(
        self,
        request_data: Dict[str, Any],
    ) -> BaseResponse:
        """
        获取指定时间断面数据,最近15分钟最后一包数据 (异步).
        
        Args:
            request_data: 请求数据
            
        Returns:
            完整的API响应，包含 {code, msg, data} 结构
            
        Raises:
            MeOSAPIError: If the request fails
        """
        response = await self._make_async_request(
            method="POST",
            url="/p/calc/api/history/getTimeSliceData",
            response_model=BaseResponse,
            json_data=request_data,
        )
        
        return response
