"""
Base HTTP client for MeOS Python SDK.

This module provides the core HTTP client functionality with authentication,
retry logic, and error handling.
"""

import asyncio
import logging
import time
from typing import Any, Dict, Optional

import httpx

from .auth import TokenManager
from .config import ClientConfig
from .exceptions import (
    MeOSConnectionError,
    MeOSTimeoutError,
    create_api_error,
)

logger = logging.getLogger(__name__)


class BaseHTTPClient:
    """Base HTTP client with authentication and retry logic."""

    def __init__(self, config: ClientConfig) -> None:
        """
        Initialize the base HTTP client.

        Args:
            config: Client configuration
        """
        self.config = config
        self._setup_logging()

        # Initialize token manager
        self.token_manager = TokenManager(
            app_id=config.app_id,
            app_secret=config.app_secret,
            base_url=config.base_url,
            token_endpoint=config.token_endpoint,
            token_lifetime=config.token_lifetime,
            refresh_threshold=config.token_refresh_threshold,
        )

        # Initialize HTTP clients
        self._sync_client: Optional[httpx.Client] = None
        self._async_client: Optional[httpx.AsyncClient] = None

    def _setup_logging(self) -> None:
        """Setup logging configuration."""
        logging.basicConfig(
            level=self.config.logging.level,
            format=self.config.logging.format,
        )

    def _get_sync_client(self) -> httpx.Client:
        """Get or create synchronous HTTP client."""
        if self._sync_client is None:
            self._sync_client = httpx.Client(
                base_url=self.config.base_url,
                timeout=httpx.Timeout(**self.config.timeout.to_httpx_timeout()),
                verify=self.config.verify_ssl,
                follow_redirects=self.config.follow_redirects,
                limits=httpx.Limits(
                    max_connections=self.config.max_connections,
                    max_keepalive_connections=self.config.max_keepalive_connections,
                    keepalive_expiry=self.config.keepalive_expiry,
                ),
                headers=self.config.extra_headers,
            )
        return self._sync_client

    def _get_async_client(self) -> httpx.AsyncClient:
        """Get or create asynchronous HTTP client."""
        if self._async_client is None:
            self._async_client = httpx.AsyncClient(
                base_url=self.config.base_url,
                timeout=httpx.Timeout(**self.config.timeout.to_httpx_timeout()),
                verify=self.config.verify_ssl,
                follow_redirects=self.config.follow_redirects,
                limits=httpx.Limits(
                    max_connections=self.config.max_connections,
                    max_keepalive_connections=self.config.max_keepalive_connections,
                    keepalive_expiry=self.config.keepalive_expiry,
                ),
                headers=self.config.extra_headers,
            )
        return self._async_client

    def _prepare_headers(self, headers: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """Prepare request headers with authentication."""
        request_headers = {}

        # Add authentication headers
        request_headers.update(self.token_manager.get_auth_headers())

        # Add custom headers
        if headers:
            request_headers.update(headers)

        return request_headers

    def _log_request(self, method: str, url: str, **kwargs) -> None:
        """Log request details if enabled."""
        if self.config.logging.log_requests:
            logger.debug(f"Request: {method.upper()} {url}")

            if self.config.logging.log_request_bodies and "json" in kwargs:
                logger.debug(f"Request body: {kwargs['json']}")

    def _log_response(self, response: httpx.Response) -> None:
        """Log response details if enabled."""
        if self.config.logging.log_responses:
            logger.debug(f"Response: {response.status_code} {response.reason_phrase}")

            if self.config.logging.log_response_bodies:
                try:
                    logger.debug(f"Response body: {response.text}")
                except Exception:
                    logger.debug("Response body: <unable to decode>")

    def _handle_response_errors(self, response: httpx.Response) -> None:
        """Handle HTTP response errors."""
        if response.status_code >= 400:
            try:
                response_data = response.json()
            except Exception:
                response_data = {}

            raise create_api_error(response.status_code, response_data)

    def _calculate_backoff_delay(self, attempt: int) -> float:
        """Calculate backoff delay for retry attempts."""
        delay = self.config.retry.backoff_factor * (2**attempt)
        return min(delay, self.config.retry.backoff_max)

    def request(
        self,
        method: str,
        url: str,
        **kwargs: Any,
    ) -> httpx.Response:
        """
        Make a synchronous HTTP request with retry logic.

        Args:
            method: HTTP method
            url: Request URL
            **kwargs: Additional request parameters

        Returns:
            HTTP response

        Raises:
            MeOSError: If the request fails after all retries
        """
        client = self._get_sync_client()

        # Ensure authentication
        token = self.token_manager.authenticate_sync(client)

        # Prepare headers
        headers = kwargs.pop("headers", {})
        headers = self._prepare_headers(headers)
        kwargs["headers"] = headers

        # Log request
        self._log_request(method, url, **kwargs)

        # Retry logic
        last_exception = None

        for attempt in range(self.config.retry.max_retries + 1):
            try:
                response = client.request(method, url, **kwargs)
                self._log_response(response)

                # Handle authentication errors by invalidating token
                if response.status_code == 401:
                    self.token_manager.invalidate_token()
                    if attempt < self.config.retry.max_retries:
                        # Retry with new token
                        token = self.token_manager.authenticate_sync(client)
                        headers = self._prepare_headers(headers)
                        kwargs["headers"] = headers
                        continue

                # Check for retryable status codes
                if response.status_code in self.config.retry.retry_on_status and attempt < self.config.retry.max_retries:
                    delay = self._calculate_backoff_delay(attempt)
                    logger.warning(f"Request failed with status {response.status_code}, retrying in {delay:.2f}s (attempt {attempt + 1})")
                    time.sleep(delay)
                    continue

                # Handle response errors
                self._handle_response_errors(response)

                return response

            except httpx.RequestError as e:
                last_exception = e
                if self.config.retry.retry_on_connection_error and attempt < self.config.retry.max_retries:
                    delay = self._calculate_backoff_delay(attempt)
                    logger.warning(f"Connection error: {e}, retrying in {delay:.2f}s (attempt {attempt + 1})")
                    time.sleep(delay)
                    continue
                break

        # All retries exhausted
        if last_exception:
            if isinstance(last_exception, httpx.TimeoutException):
                raise MeOSTimeoutError(
                    f"Request timed out after {self.config.retry.max_retries + 1} attempts",
                    timeout=self.config.timeout.total,
                )
            else:
                raise MeOSConnectionError(
                    f"Connection failed after {self.config.retry.max_retries + 1} attempts",
                    original_error=last_exception,
                )

        # This should not happen, but just in case
        raise MeOSConnectionError("Request failed for unknown reason")

    async def arequest(
        self,
        method: str,
        url: str,
        **kwargs: Any,
    ) -> httpx.Response:
        """
        Make an asynchronous HTTP request with retry logic.

        Args:
            method: HTTP method
            url: Request URL
            **kwargs: Additional request parameters

        Returns:
            HTTP response

        Raises:
            MeOSError: If the request fails after all retries
        """
        client = self._get_async_client()

        # Ensure authentication
        token = await self.token_manager.authenticate_async(client)

        # Prepare headers
        headers = kwargs.pop("headers", {})
        headers = self._prepare_headers(headers)
        kwargs["headers"] = headers

        # Log request
        self._log_request(method, url, **kwargs)

        # Retry logic
        last_exception = None

        for attempt in range(self.config.retry.max_retries + 1):
            try:
                response = await client.request(method, url, **kwargs)
                self._log_response(response)

                # Handle authentication errors by invalidating token
                if response.status_code == 401:
                    self.token_manager.invalidate_token()
                    if attempt < self.config.retry.max_retries:
                        # Retry with new token
                        token = await self.token_manager.authenticate_async(client)
                        headers = self._prepare_headers(headers)
                        kwargs["headers"] = headers
                        continue

                # Check for retryable status codes
                if response.status_code in self.config.retry.retry_on_status and attempt < self.config.retry.max_retries:
                    delay = self._calculate_backoff_delay(attempt)
                    logger.warning(f"Request failed with status {response.status_code}, retrying in {delay:.2f}s (attempt {attempt + 1})")
                    await asyncio.sleep(delay)
                    continue

                # Handle response errors
                self._handle_response_errors(response)

                return response

            except httpx.RequestError as e:
                last_exception = e
                if self.config.retry.retry_on_connection_error and attempt < self.config.retry.max_retries:
                    delay = self._calculate_backoff_delay(attempt)
                    logger.warning(f"Connection error: {e}, retrying in {delay:.2f}s (attempt {attempt + 1})")
                    await asyncio.sleep(delay)
                    continue
                break

        # All retries exhausted
        if last_exception:
            if isinstance(last_exception, httpx.TimeoutException):
                raise MeOSTimeoutError(
                    f"Request timed out after {self.config.retry.max_retries + 1} attempts",
                    timeout=self.config.timeout.total,
                )
            else:
                raise MeOSConnectionError(
                    f"Connection failed after {self.config.retry.max_retries + 1} attempts",
                    original_error=last_exception,
                )

        # This should not happen, but just in case
        raise MeOSConnectionError("Request failed for unknown reason")

    def close(self) -> None:
        """Close the HTTP clients."""
        if self._sync_client:
            self._sync_client.close()

    async def aclose(self) -> None:
        """Close the async HTTP client."""
        if self._async_client:
            await self._async_client.aclose()

    def __enter__(self) -> "BaseHTTPClient":
        """Context manager entry."""
        return self

    def __exit__(self, exc_type: Any, exc_val: Any, exc_tb: Any) -> None:
        """Context manager exit."""
        self.close()

    async def __aenter__(self) -> "BaseHTTPClient":
        """Async context manager entry."""
        return self

    async def __aexit__(self, exc_type: Any, exc_val: Any, exc_tb: Any) -> None:
        """Async context manager exit."""
        await self.aclose()
