"""
Instance models for MeOS Python SDK.

This module contains models for various instance types in the MeOS system.
"""

from typing import List, Optional

from pydantic import Field

from .base import BaseModel, BaseResponse, PageResponse
from .common import StaticPropertyValue, DynamicPropertyValue, InstanceTreeNode


class BaseInstanceInfo(BaseModel):
    """Base instance information model."""
    
    data_code: str = Field(..., description="数据编码")
    name: str = Field(..., description="名称")
    description: Optional[str] = Field(None, description="描述")
    reserve1: Optional[str] = Field(None, description="预留1")
    reserve2: Optional[str] = Field(None, description="预留2")
    reserve3: Optional[str] = Field(None, description="预留3")
    reserve4: Optional[str] = Field(None, description="预留4")
    reserve5: Optional[str] = Field(None, description="预留5")


class ProjectInstance(BaseInstanceInfo):
    """Project instance model."""
    
    pro_type: Optional[str] = Field(None, description="项目类别")
    pro_name: str = Field(..., description="项目名称")
    
    # Override name field to use pro_name
    name: str = Field(..., alias="pro_name", description="项目名称")


class StationInstance(BaseInstanceInfo):
    """Station instance model."""
    
    station_name: str = Field(..., description="站点名称")
    station_type: Optional[str] = Field(None, description="站点类型")
    
    # Override name field to use station_name
    name: str = Field(..., alias="station_name", description="站点名称")


class EquipmentInstance(BaseInstanceInfo):
    """Equipment instance model."""
    
    equipment_name: str = Field(..., description="设备名称")
    equipment_type: Optional[str] = Field(None, description="设备类型")
    model: Optional[str] = Field(None, description="设备型号")
    manufacturer: Optional[str] = Field(None, description="制造商")
    
    # Override name field to use equipment_name
    name: str = Field(..., alias="equipment_name", description="设备名称")


class UnitInstance(BaseInstanceInfo):
    """Unit instance model."""
    
    unit_name: str = Field(..., description="单元名称")
    unit_type: Optional[str] = Field(None, description="单元类型")
    
    # Override name field to use unit_name
    name: str = Field(..., alias="unit_name", description="单元名称")


class ElementInstance(BaseInstanceInfo):
    """Element instance model."""
    
    element_name: str = Field(..., description="元件名称")
    element_type: Optional[str] = Field(None, description="元件类型")
    
    # Override name field to use element_name
    name: str = Field(..., alias="element_name", description="元件名称")


class SystemInstance(BaseInstanceInfo):
    """System instance model."""
    
    system_name: str = Field(..., description="系统名称")
    system_type: Optional[str] = Field(None, description="系统类型")
    
    # Override name field to use system_name
    name: str = Field(..., alias="system_name", description="系统名称")


class MeterInstance(BaseInstanceInfo):
    """Meter instance model."""
    
    meter_name: str = Field(..., description="表计名称")
    meter_type: Optional[str] = Field(None, description="表计类型")
    
    # Override name field to use meter_name
    name: str = Field(..., alias="meter_name", description="表计名称")


class PipeInstance(BaseInstanceInfo):
    """Pipe instance model."""
    
    pipe_name: str = Field(..., description="管路名称")
    pipe_type: Optional[str] = Field(None, description="管路类型")
    
    # Override name field to use pipe_name
    name: str = Field(..., alias="pipe_name", description="管路名称")


class ProductionEnergySystemInstance(BaseInstanceInfo):
    """Production energy system instance model."""
    
    system_name: str = Field(..., description="生产能耗系统名称")
    system_type: Optional[str] = Field(None, description="系统类型")
    
    # Override name field to use system_name
    name: str = Field(..., alias="system_name", description="系统名称")


class FactoryAreaInstance(BaseInstanceInfo):
    """Factory area instance model."""
    
    area_name: str = Field(..., description="厂区名称")
    area_type: Optional[str] = Field(None, description="厂区类型")
    
    # Override name field to use area_name
    name: str = Field(..., alias="area_name", description="厂区名称")


class FactoryBuildingInstance(BaseInstanceInfo):
    """Factory building instance model."""
    
    building_name: str = Field(..., description="厂房名称")
    building_type: Optional[str] = Field(None, description="厂房类型")
    
    # Override name field to use building_name
    name: str = Field(..., alias="building_name", description="厂房名称")


class WorkshopInstance(BaseInstanceInfo):
    """Workshop instance model."""
    
    workshop_name: str = Field(..., description="车间名称")
    workshop_type: Optional[str] = Field(None, description="车间类型")
    
    # Override name field to use workshop_name
    name: str = Field(..., alias="workshop_name", description="车间名称")


class ProductionLineInstance(BaseInstanceInfo):
    """Production line instance model."""
    
    line_name: str = Field(..., description="产线名称")
    line_type: Optional[str] = Field(None, description="产线类型")
    
    # Override name field to use line_name
    name: str = Field(..., alias="line_name", description="产线名称")


class ProcessInstance(BaseInstanceInfo):
    """Process instance model."""
    
    process_name: str = Field(..., description="工序名称")
    process_type: Optional[str] = Field(None, description="工序类型")
    
    # Override name field to use process_name
    name: str = Field(..., alias="process_name", description="工序名称")


class ProductionEquipmentInstance(BaseInstanceInfo):
    """Production equipment instance model."""
    
    equipment_name: str = Field(..., description="设备名称")
    equipment_type: Optional[str] = Field(None, description="设备类型")
    
    # Override name field to use equipment_name
    name: str = Field(..., alias="equipment_name", description="设备名称")


# Response models
class ProjectInstanceResponse(BaseResponse[ProjectInstance]):
    """Project instance response model."""
    pass


class StationInstanceResponse(BaseResponse[StationInstance]):
    """Station instance response model."""
    pass


class EquipmentInstanceResponse(BaseResponse[EquipmentInstance]):
    """Equipment instance response model."""
    pass


class UnitInstanceResponse(BaseResponse[UnitInstance]):
    """Unit instance response model."""
    pass


class ElementInstanceResponse(BaseResponse[ElementInstance]):
    """Element instance response model."""
    pass


class SystemInstanceResponse(BaseResponse[SystemInstance]):
    """System instance response model."""
    pass


class MeterInstanceResponse(BaseResponse[MeterInstance]):
    """Meter instance response model."""
    pass


class PipeInstanceResponse(BaseResponse[PipeInstance]):
    """Pipe instance response model."""
    pass


# List response models
class ProjectInstanceListResponse(BaseResponse[List[ProjectInstance]]):
    """Project instance list response model."""
    pass


class InstanceTreeResponse(BaseResponse[List[InstanceTreeNode]]):
    """Instance tree response model."""
    pass


# Property response models
class StaticPropertyListResponse(BaseResponse[PageResponse[StaticPropertyValue]]):
    """Static property list response model."""
    pass


class DynamicPropertyListResponse(BaseResponse[PageResponse[DynamicPropertyValue]]):
    """Dynamic property list response model."""
    pass
