"""
Integration tests for MeOS Python SDK.

These tests require a running MeOS instance and valid credentials.
They are marked with @pytest.mark.integration and can be skipped
by running: pytest -m "not integration"
"""

import pytest
import os
from datetime import datetime, timedelta

from meos import MeOSClient, AsyncMeOSClient
from meos.exceptions import MeOSAPIError, MeOSAuthenticationError


# Test configuration from environment variables
TEST_BASE_URL = os.getenv("MEOS_TEST_BASE_URL")
TEST_APP_ID = os.getenv("MEOS_TEST_APP_ID")
TEST_APP_SECRET = os.getenv("MEOS_TEST_APP_SECRET")
TEST_PROJECT_CODE = os.getenv("MEOS_TEST_PROJECT_CODE", "PROJECT_001")
TEST_DATA_CODES = os.getenv("MEOS_TEST_DATA_CODES", "TEMP_001,PRESSURE_001").split(",")

# Skip integration tests if credentials are not provided
pytestmark = pytest.mark.skipif(
    not all([TEST_BASE_URL, TEST_APP_ID, TEST_APP_SECRET]),
    reason="Integration test credentials not provided"
)


@pytest.mark.integration
@pytest.mark.network
class TestMeOSClientIntegration:
    """Integration tests for MeOSClient."""
    
    def test_client_authentication(self):
        """Test client authentication with real credentials."""
        with MeOSClient(
            base_url=TEST_BASE_URL,
            app_id=TEST_APP_ID,
            app_secret=TEST_APP_SECRET
        ) as client:
            # Test authentication
            assert client.auth.is_authenticated() or not client.auth.is_authenticated()
            
            # Get authentication info
            auth_info = client.auth.get_current_auth_info()
            assert auth_info.app_id == TEST_APP_ID
    
    def test_invalid_authentication(self):
        """Test authentication with invalid credentials."""
        with pytest.raises((MeOSAuthenticationError, MeOSAPIError)):
            with MeOSClient(
                base_url=TEST_BASE_URL,
                app_id="invalid_app_id",
                app_secret="invalid_secret"
            ) as client:
                # This should trigger authentication and fail
                response = client.project_instances.get_project_info()
                if not response.is_success and response.code == 401:
                    raise MeOSAuthenticationError(response.msg)
    
    def test_project_operations(self):
        """Test project-related operations."""
        with MeOSClient(
            base_url=TEST_BASE_URL,
            app_id=TEST_APP_ID,
            app_secret=TEST_APP_SECRET
        ) as client:
            # List projects
            projects_response = client.project_instances.list_project_instances()
            assert projects_response.code in [200, 400, 404]  # Accept various valid responses
            
            if projects_response.is_success and projects_response.data:
                # Get first project info
                first_project = projects_response.data[0]
                project_code = first_project.get("data_code") or TEST_PROJECT_CODE
                
                # Get specific project info
                project_response = client.project_instances.get_project_info(project_code)
                assert project_response.code in [200, 404]  # Project may not exist
                
                if project_response.is_success:
                    assert project_response.data is not None
                    assert "data_code" in project_response.data or hasattr(project_response.data, "data_code")
    
    def test_dynamic_data_operations(self):
        """Test dynamic data operations."""
        with MeOSClient(
            base_url=TEST_BASE_URL,
            app_id=TEST_APP_ID,
            app_secret=TEST_APP_SECRET
        ) as client:
            # Test real-time data
            realtime_response = client.dynamic_data.get_dynamic_realtime(
                data_codes=TEST_DATA_CODES[:2]  # Use first 2 data codes
            )
            assert realtime_response.code in [200, 400, 404]
            
            if realtime_response.is_success:
                assert realtime_response.data is not None
            
            # Test historical data
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=1)
            
            history_response = client.dynamic_data.get_dynamic_history(
                data_codes=TEST_DATA_CODES[:1],  # Use first data code
                start_time=start_time.strftime("%Y-%m-%d %H:%M:%S"),
                end_time=end_time.strftime("%Y-%m-%d %H:%M:%S")
            )
            assert history_response.code in [200, 400, 404]
            
            if history_response.is_success:
                assert history_response.data is not None
    
    def test_error_handling(self):
        """Test error handling with invalid requests."""
        with MeOSClient(
            base_url=TEST_BASE_URL,
            app_id=TEST_APP_ID,
            app_secret=TEST_APP_SECRET
        ) as client:
            # Test with invalid project code
            response = client.project_instances.get_project_info("INVALID_PROJECT_CODE")
            # Should return error response, not raise exception
            assert response.code != 200 or response.is_success
            
            # Test with invalid data codes
            response = client.dynamic_data.get_dynamic_realtime(
                data_codes=["INVALID_DATA_CODE_123"]
            )
            # Should return error response, not raise exception
            assert response.code != 200 or response.is_success


@pytest.mark.integration
@pytest.mark.network
@pytest.mark.asyncio
class TestAsyncMeOSClientIntegration:
    """Integration tests for AsyncMeOSClient."""
    
    async def test_async_client_authentication(self):
        """Test async client authentication."""
        async with AsyncMeOSClient(
            base_url=TEST_BASE_URL,
            app_id=TEST_APP_ID,
            app_secret=TEST_APP_SECRET
        ) as client:
            # Test authentication
            auth_info = client.auth.get_current_auth_info()
            assert auth_info.app_id == TEST_APP_ID
    
    async def test_async_project_operations(self):
        """Test async project operations."""
        async with AsyncMeOSClient(
            base_url=TEST_BASE_URL,
            app_id=TEST_APP_ID,
            app_secret=TEST_APP_SECRET
        ) as client:
            # List projects
            projects_response = await client.project_instances.list_project_instances_async()
            assert projects_response.code in [200, 400, 404]
            
            if projects_response.is_success and projects_response.data:
                # Get first project info
                first_project = projects_response.data[0]
                project_code = first_project.get("data_code") or TEST_PROJECT_CODE
                
                # Get specific project info
                project_response = await client.project_instances.get_project_info_async(project_code)
                assert project_response.code in [200, 404]
    
    async def test_async_dynamic_data_operations(self):
        """Test async dynamic data operations."""
        async with AsyncMeOSClient(
            base_url=TEST_BASE_URL,
            app_id=TEST_APP_ID,
            app_secret=TEST_APP_SECRET
        ) as client:
            # Test real-time data
            realtime_response = await client.dynamic_data.get_dynamic_realtime_async(
                data_codes=TEST_DATA_CODES[:2]
            )
            assert realtime_response.code in [200, 400, 404]
            
            # Test historical data
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=1)
            
            history_response = await client.dynamic_data.get_dynamic_history_async(
                data_codes=TEST_DATA_CODES[:1],
                start_time=start_time.strftime("%Y-%m-%d %H:%M:%S"),
                end_time=end_time.strftime("%Y-%m-%d %H:%M:%S")
            )
            assert history_response.code in [200, 400, 404]
    
    async def test_concurrent_requests(self):
        """Test concurrent async requests."""
        import asyncio
        
        async with AsyncMeOSClient(
            base_url=TEST_BASE_URL,
            app_id=TEST_APP_ID,
            app_secret=TEST_APP_SECRET
        ) as client:
            # Execute multiple requests concurrently
            tasks = [
                client.project_instances.list_project_instances_async(),
                client.dynamic_data.get_dynamic_realtime_async(TEST_DATA_CODES[:1]),
                client.production_energy.get_pcm_page_async(),
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # All requests should complete (successfully or with errors)
            assert len(results) == 3
            
            for result in results:
                if isinstance(result, Exception):
                    # Exception is acceptable for integration tests
                    print(f"Request failed with exception: {result}")
                else:
                    # Should be a valid response object
                    assert hasattr(result, "code")
                    assert hasattr(result, "msg")


@pytest.mark.integration
@pytest.mark.network
class TestProductionEnergyIntegration:
    """Integration tests for production energy operations."""
    
    def test_pcm_operations(self):
        """Test PCM (Production Control Management) operations."""
        with MeOSClient(
            base_url=TEST_BASE_URL,
            app_id=TEST_APP_ID,
            app_secret=TEST_APP_SECRET
        ) as client:
            # Get PCM page
            pcm_response = client.production_energy.get_pcm_page()
            assert pcm_response.code in [200, 400, 404]
            
            if pcm_response.is_success and pcm_response.data:
                # If we have PCM data, try to get info for first PCM
                pcm_list = pcm_response.data
                if isinstance(pcm_list, list) and pcm_list:
                    first_pcm = pcm_list[0]
                    pcm_code = first_pcm.get("data_code")
                    
                    if pcm_code:
                        # Get PCM info
                        info_response = client.production_energy.get_pcm_info(pcm_code)
                        assert info_response.code in [200, 404]
                        
                        # Get PCM tree
                        tree_response = client.production_energy.get_pcm_tree(pcm_code)
                        assert tree_response.code in [200, 404]


@pytest.mark.integration
@pytest.mark.network
class TestControlIntegration:
    """Integration tests for control operations."""
    
    def test_control_operations(self):
        """Test control operations (read-only tests)."""
        with MeOSClient(
            base_url=TEST_BASE_URL,
            app_id=TEST_APP_ID,
            app_secret=TEST_APP_SECRET
        ) as client:
            # Test feedback value query (safe read operation)
            query_data = {
                "data_codes": TEST_DATA_CODES[:1]
            }
            
            feedback_response = client.control.query_feedback_value(query_data)
            assert feedback_response.code in [200, 400, 404]
            
            # Note: We don't test control command issuing in integration tests
            # as it could affect real systems


if __name__ == "__main__":
    # Run integration tests
    pytest.main([__file__, "-m", "integration", "-v"])
