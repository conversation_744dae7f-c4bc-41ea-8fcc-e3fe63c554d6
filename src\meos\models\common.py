"""
Common models for MeOS Python SDK.

This module contains commonly used models across different API operations.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from pydantic import Field

from .base import BaseModel, PageResponse, TreeNode


class CodeNamePair(BaseModel):
    """Code and name pair model."""
    
    code: str = Field(..., description="编码")
    name: str = Field(..., description="名称")


class InstanceTreeNode(TreeNode[Dict[str, Any]]):
    """Instance tree node model."""
    
    data_code: str = Field(..., description="数据编码")
    instance_name: str = Field(..., description="实例名称")
    instance_type: str = Field(..., description="实例类型")
    model_code: Optional[str] = Field(None, description="模型编码")
    parent_code: Optional[str] = Field(None, description="父级编码")
    
    # Override base fields with specific names
    id: str = Field(..., alias="data_code", description="节点ID（数据编码）")
    name: str = Field(..., alias="instance_name", description="节点名称（实例名称）")
    parent_id: Optional[str] = Field(None, alias="parent_code", description="父节点ID（父级编码）")


class PropertyInfo(BaseModel):
    """Property information model."""
    
    property_code: str = Field(..., description="属性编码")
    property_name: str = Field(..., description="属性名称")
    data_type: str = Field(..., description="数据类型")
    unit: Optional[str] = Field(None, description="单位")
    description: Optional[str] = Field(None, description="描述")
    is_required: bool = Field(False, description="是否必填")
    default_value: Optional[Union[str, int, float, bool]] = Field(None, description="默认值")
    min_value: Optional[Union[int, float]] = Field(None, description="最小值")
    max_value: Optional[Union[int, float]] = Field(None, description="最大值")
    enum_values: Optional[List[str]] = Field(None, description="枚举值")


class StaticPropertyValue(BaseModel):
    """Static property value model."""
    
    property_code: str = Field(..., description="属性编码")
    property_name: Optional[str] = Field(None, description="属性名称")
    value: Optional[Union[str, int, float, bool]] = Field(None, description="属性值")
    unit: Optional[str] = Field(None, description="单位")
    data_type: Optional[str] = Field(None, description="数据类型")


class DynamicPropertyValue(BaseModel):
    """Dynamic property value model."""
    
    data_code: str = Field(..., description="数据编码")
    property_code: str = Field(..., description="属性编码")
    property_name: Optional[str] = Field(None, description="属性名称")
    value: Optional[Union[str, int, float, bool]] = Field(None, description="属性值")
    unit: Optional[str] = Field(None, description="单位")
    data_type: Optional[str] = Field(None, description="数据类型")
    timestamp: Optional[datetime] = Field(None, description="时间戳")
    quality: Optional[str] = Field(None, description="数据质量")
    
    @property
    def is_good_quality(self) -> bool:
        """Check if the data quality is good."""
        return self.quality == "GOOD" if self.quality else True


class TimeRange(BaseModel):
    """Time range model."""
    
    start_time: datetime = Field(..., description="开始时间")
    end_time: datetime = Field(..., description="结束时间")
    
    def __post_init__(self) -> None:
        """Validate time range."""
        if self.start_time >= self.end_time:
            raise ValueError("start_time must be before end_time")
    
    @property
    def duration_seconds(self) -> float:
        """Get duration in seconds."""
        return (self.end_time - self.start_time).total_seconds()
    
    @property
    def duration_minutes(self) -> float:
        """Get duration in minutes."""
        return self.duration_seconds / 60
    
    @property
    def duration_hours(self) -> float:
        """Get duration in hours."""
        return self.duration_seconds / 3600


class PaginationRequest(BaseModel):
    """Pagination request model."""
    
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(10, ge=1, le=1000, description="每页大小")
    
    @property
    def offset(self) -> int:
        """Calculate offset for database queries."""
        return (self.page - 1) * self.size


class SortOrder(BaseModel):
    """Sort order model."""
    
    field: str = Field(..., description="排序字段")
    direction: str = Field("asc", regex="^(asc|desc)$", description="排序方向")
    
    @property
    def is_ascending(self) -> bool:
        """Check if sort direction is ascending."""
        return self.direction.lower() == "asc"
    
    @property
    def is_descending(self) -> bool:
        """Check if sort direction is descending."""
        return self.direction.lower() == "desc"


class QueryFilter(BaseModel):
    """Query filter model."""
    
    field: str = Field(..., description="过滤字段")
    operator: str = Field(..., description="操作符")
    value: Union[str, int, float, bool, List[Any]] = Field(..., description="过滤值")
    
    # Common operators
    EQUALS = "eq"
    NOT_EQUALS = "ne"
    GREATER_THAN = "gt"
    GREATER_THAN_OR_EQUAL = "gte"
    LESS_THAN = "lt"
    LESS_THAN_OR_EQUAL = "lte"
    IN = "in"
    NOT_IN = "not_in"
    LIKE = "like"
    NOT_LIKE = "not_like"
    IS_NULL = "is_null"
    IS_NOT_NULL = "is_not_null"


class SearchRequest(BaseModel):
    """Search request model."""
    
    keyword: Optional[str] = Field(None, description="搜索关键词")
    filters: List[QueryFilter] = Field(default_factory=list, description="过滤条件")
    sort: List[SortOrder] = Field(default_factory=list, description="排序条件")
    pagination: PaginationRequest = Field(default_factory=PaginationRequest, description="分页参数")


class BulkOperationResult(BaseModel):
    """Bulk operation result model."""
    
    total: int = Field(..., description="总数")
    success: int = Field(..., description="成功数")
    failed: int = Field(..., description="失败数")
    errors: List[str] = Field(default_factory=list, description="错误信息")
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate."""
        return self.success / self.total if self.total > 0 else 0.0
    
    @property
    def is_all_success(self) -> bool:
        """Check if all operations succeeded."""
        return self.failed == 0
    
    @property
    def has_errors(self) -> bool:
        """Check if there are any errors."""
        return self.failed > 0
