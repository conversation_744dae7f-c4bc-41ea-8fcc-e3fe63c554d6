"""
Control service for MeOS Python SDK.

This module provides control operations.
Tag: 控制接口
"""

import logging
from typing import Any, Dict, List

from .base_service import BaseService
from ..models.base import BaseResponse

logger = logging.getLogger(__name__)


class ControlService(BaseService):
    """Control service for MeOS API - 控制接口."""
    
    def issue_control_command(
        self,
        command_data: Dict[str, Any],
    ) -> BaseResponse:
        """
        下发控制命令.
        
        Args:
            command_data: 控制命令数据
            
        Returns:
            完整的API响应，包含 {code, msg, data} 结构
            
        Raises:
            MeOSAPIError: If the request fails
        """
        response = self._make_request(
            method="POST",
            url="/p/control/issue",
            response_model=BaseResponse,
            json_data=command_data,
        )
        
        return response
    
    async def issue_control_command_async(
        self,
        command_data: Dict[str, Any],
    ) -> BaseResponse:
        """
        下发控制命令 (异步).
        
        Args:
            command_data: 控制命令数据
            
        Returns:
            完整的API响应，包含 {code, msg, data} 结构
            
        Raises:
            MeOSAPIError: If the request fails
        """
        response = await self._make_async_request(
            method="POST",
            url="/p/control/issue",
            response_model=BaseResponse,
            json_data=command_data,
        )
        
        return response
    
    def query_feedback_value(
        self,
        query_data: Dict[str, Any],
    ) -> BaseResponse:
        """
        数据实时值查询.
        
        Args:
            query_data: 查询数据
            
        Returns:
            完整的API响应，包含 {code, msg, data} 结构
            
        Raises:
            MeOSAPIError: If the request fails
        """
        response = self._make_request(
            method="POST",
            url="/p/control/queryFeedbackVal",
            response_model=BaseResponse,
            json_data=query_data,
        )
        
        return response
    
    async def query_feedback_value_async(
        self,
        query_data: Dict[str, Any],
    ) -> BaseResponse:
        """
        数据实时值查询 (异步).
        
        Args:
            query_data: 查询数据
            
        Returns:
            完整的API响应，包含 {code, msg, data} 结构
            
        Raises:
            MeOSAPIError: If the request fails
        """
        response = await self._make_async_request(
            method="POST",
            url="/p/control/queryFeedbackVal",
            response_model=BaseResponse,
            json_data=query_data,
        )
        
        return response
