# MeOS Python SDK API Reference

This document provides a comprehensive reference for all classes, methods, and models in the MeOS Python SDK.

## Table of Contents

1. [Client Classes](#client-classes)
2. [Service Classes](#service-classes)
3. [Model Classes](#model-classes)
4. [Exception Classes](#exception-classes)
5. [Configuration Classes](#configuration-classes)

## Client Classes

### MeOSClient

Synchronous client for MeOS API.

```python
class MeOSClient:
    def __init__(
        self,
        base_url: str,
        app_id: str,
        app_secret: str,
        **kwargs
    ) -> None
```

**Parameters:**
- `base_url`: Base URL for the MeOS API
- `app_id`: Application ID for authentication
- `app_secret`: Application secret for authentication
- `**kwargs`: Additional configuration options

**Properties:**
- `auth: AuthService` - Authentication service
- `dynamic_data: DynamicDataService` - Dynamic data service
- `project_instances: ProjectInstanceService` - Project instance service
- `production_energy: ProductionEnergyService` - Production energy service
- `control: ControlService` - Control service
- `calculation: CalculationService` - Calculation service
- `topology: TopologyService` - Topology service

**Methods:**
- `close() -> None` - Close the client and clean up resources
- `__enter__() -> MeOSClient` - Context manager entry
- `__exit__(...) -> None` - Context manager exit

### AsyncMeOSClient

Asynchronous client for MeOS API.

```python
class AsyncMeOSClient:
    def __init__(
        self,
        base_url: str,
        app_id: str,
        app_secret: str,
        **kwargs
    ) -> None
```

**Parameters:** Same as `MeOSClient`

**Properties:** Same as `MeOSClient`

**Methods:**
- `async aclose() -> None` - Close the async client
- `async __aenter__() -> AsyncMeOSClient` - Async context manager entry
- `async __aexit__(...) -> None` - Async context manager exit

## Service Classes

### AuthService

Authentication service for token management.

**Methods:**

#### get_token
```python
def get_token(self, app_id: str, app_secret: str) -> TokenResponse
```
Get authentication token.

#### get_token_async
```python
async def get_token_async(self, app_id: str, app_secret: str) -> TokenResponse
```
Get authentication token asynchronously.

#### get_current_auth_info
```python
def get_current_auth_info(self) -> AuthenticationInfo
```
Get current authentication information.

#### invalidate_token
```python
def invalidate_token(self) -> None
```
Invalidate the current authentication token.

#### refresh_token
```python
def refresh_token(self) -> str
```
Refresh the authentication token.

#### is_authenticated
```python
def is_authenticated(self) -> bool
```
Check if the client is currently authenticated.

### DynamicDataService

Service for dynamic property data operations.

**Methods:**

#### get_dynamic_history
```python
def get_dynamic_history(
    self,
    data_codes: List[str],
    start_time: str,
    end_time: str,
) -> DynamicHistoryResponse
```
获取动态属性历史数据.

**Parameters:**
- `data_codes`: 属性编码列表
- `start_time`: 开始时间
- `end_time`: 结束时间

#### get_dynamic_realtime
```python
def get_dynamic_realtime(
    self,
    data_codes: List[str],
    timestamp: Optional[str] = None,
) -> DynamicRealtimeResponse
```
获取动态属性实时数据.

#### get_dynamic_realtime_with_formula
```python
def get_dynamic_realtime_with_formula(
    self,
    data_codes: List[str],
    timestamp: Optional[str] = None,
    include_formula: bool = True,
) -> DynamicRealtimeWithFormulaResponse
```
获取动态属性实时数据（含公式计算）.

### ProjectInstanceService

Service for project instance operations.

**Methods:**

#### get_project_info
```python
def get_project_info(self, data_code: Optional[str] = None) -> ProjectInstanceResponse
```
获取项目实例详细信息.

#### list_project_instances
```python
def list_project_instances(self) -> ProjectInstanceListResponse
```
查询项目实例列表.

#### get_project_tree
```python
def get_project_tree(self, data_code: str) -> InstanceTreeResponse
```
查询项目实例所有下级（树）.

#### get_base_properties
```python
def get_base_properties(
    self,
    pagination: PaginationRequest,
    data_code: Optional[str] = None,
) -> StaticPropertyListResponse
```
获取基础属性列表.

#### get_dynamic_properties
```python
def get_dynamic_properties(
    self,
    pagination: PaginationRequest,
    data_code: Optional[str] = None,
) -> DynamicPropertyListResponse
```
获取动态属性列表.

#### get_static_properties
```python
def get_static_properties(
    self,
    pagination: PaginationRequest,
    data_code: Optional[str] = None,
) -> StaticPropertyListResponse
```
获取静态属性列表.

### ProductionEnergyService

Service for production energy system operations.

**Methods:**

#### get_pcm_info
```python
def get_pcm_info(self, data_code: Optional[str] = None) -> BaseResponse
```
获取生产能耗系统详细信息.

#### get_pcm_page
```python
def get_pcm_page(self, project_code: Optional[str] = None) -> BaseResponse
```
获取项目下生产能耗系统.

#### get_pcm_tree
```python
def get_pcm_tree(self, data_code: str) -> BaseResponse
```
查询生产能耗系统所有下级(树).

### ControlService

Service for control operations.

**Methods:**

#### issue_control_command
```python
def issue_control_command(
    self,
    command_data: Dict[str, Any],
) -> BaseResponse
```
下发控制命令.

#### query_feedback_value
```python
def query_feedback_value(
    self,
    query_data: Dict[str, Any],
) -> BaseResponse
```
数据实时值查询.

### CalculationService

Service for calculation framework operations.

**Methods:**

#### get_equally_spaced_time_data
```python
def get_equally_spaced_time_data(
    self,
    request_data: Dict[str, Any],
) -> BaseResponse
```
获取历史数据等时间间隔采样数据.

#### get_time_interval_data
```python
def get_time_interval_data(
    self,
    request_data: Dict[str, Any],
) -> BaseResponse
```
获取时间区间数据.

#### get_time_slice_data
```python
def get_time_slice_data(
    self,
    request_data: Dict[str, Any],
) -> BaseResponse
```
获取指定时间断面数据,最近15分钟最后一包数据.

### TopologyService

Service for topology relationship operations.

**Methods:**

#### get_topology
```python
def get_topology(self, data_code: Optional[str] = None) -> BaseResponse
```
拓扑关系.

## Model Classes

### Base Models

#### BaseResponse[T]
```python
class BaseResponse(BaseModel, Generic[T]):
    code: int = Field(..., description="响应状态码")
    msg: str = Field(..., description="响应消息")
    data: Optional[T] = Field(None, description="响应数据")
    
    @property
    def is_success(self) -> bool
    
    @property
    def is_error(self) -> bool
```

#### PageResponse[T]
```python
class PageResponse(BaseModel, Generic[T]):
    records: List[T] = Field(default_factory=list, description="记录列表")
    total: int = Field(..., description="总记录数")
    size: int = Field(..., description="每页大小")
    current: int = Field(..., description="当前页码")
    pages: int = Field(..., description="总页数")
    
    @property
    def has_next(self) -> bool
    
    @property
    def has_previous(self) -> bool
```

#### TreeNode[T]
```python
class TreeNode(BaseModel, Generic[T]):
    id: str = Field(..., description="节点ID")
    name: str = Field(..., description="节点名称")
    parent_id: Optional[str] = Field(None, description="父节点ID")
    level: int = Field(0, description="节点层级")
    data: Optional[T] = Field(None, description="节点数据")
    children: List["TreeNode[T]"] = Field(default_factory=list, description="子节点")
    
    @property
    def is_root(self) -> bool
    
    @property
    def is_leaf(self) -> bool
    
    def find_child(self, child_id: str) -> Optional["TreeNode[T]"]
    
    def find_descendant(self, node_id: str) -> Optional["TreeNode[T]"]
```

### Authentication Models

#### TokenRequest
```python
class TokenRequest(BaseModel):
    appId: str = Field(..., description="应用ID")
    appSecret: str = Field(..., description="应用密钥")
```

#### TokenData
```python
class TokenData(BaseModel):
    token: str = Field(..., description="Siact-token")
```

#### TokenResponse
```python
class TokenResponse(BaseResponse[TokenData]):
    data: Optional[TokenData] = Field(None, description="Token数据")
```

### Instance Models

#### ProjectInstance
```python
class ProjectInstance(BaseInstanceInfo):
    pro_type: Optional[str] = Field(None, description="项目类别")
    pro_name: str = Field(..., description="项目名称")
```

### Dynamic Data Models

#### DynamicPropertyData
```python
class DynamicPropertyData(BaseModel):
    data_code: str = Field(..., description="数据编码")
    value: Optional[Union[str, int, float, bool]] = Field(None, description="数据值")
    timestamp: Optional[datetime] = Field(None, description="时间戳")
    quality: Optional[str] = Field(None, description="数据质量")
    unit: Optional[str] = Field(None, description="单位")
    
    @property
    def is_good_quality(self) -> bool
    
    @property
    def is_numeric(self) -> bool
```

#### DynamicHistoryRequest
```python
class DynamicHistoryRequest(BaseModel):
    data_codes: List[str] = Field(..., description="属性编码列表")
    start_time: Union[str, datetime] = Field(..., description="开始时间")
    end_time: Union[str, datetime] = Field(..., description="结束时间")
```

### Common Models

#### PaginationRequest
```python
class PaginationRequest(BaseModel):
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(10, ge=1, le=1000, description="每页大小")
    
    @property
    def offset(self) -> int
```

## Exception Classes

### Base Exceptions

#### MeOSError
```python
class MeOSError(Exception):
    def __init__(
        self,
        message: str,
        code: Optional[int] = None,
        details: Optional[Dict[str, Any]] = None,
    ) -> None
```

#### MeOSAPIError
```python
class MeOSAPIError(MeOSError):
    def __init__(
        self,
        message: str,
        code: Optional[int] = None,
        status_code: Optional[int] = None,
        response_data: Optional[Dict[str, Any]] = None,
    ) -> None
```

### Specific Exceptions

- `MeOSAuthenticationError` - Authentication failures (401)
- `MeOSAuthorizationError` - Authorization failures (403)
- `MeOSNotFoundError` - Resource not found (404)
- `MeOSValidationError` - Request validation failures
- `MeOSConnectionError` - Connection failures
- `MeOSTimeoutError` - Request timeouts
- `MeOSRateLimitError` - Rate limit exceeded (429)
- `MeOSServerError` - Server errors (5xx)
- `MeOSConfigurationError` - SDK configuration errors

## Configuration Classes

### ClientConfig
```python
@dataclass
class ClientConfig:
    app_id: str
    app_secret: str
    base_url: str
    timeout: TimeoutConfig = field(default_factory=TimeoutConfig)
    retry: RetryConfig = field(default_factory=RetryConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    verify_ssl: bool = True
    follow_redirects: bool = True
    max_connections: int = 100
    # ... other configuration options
```

### TimeoutConfig
```python
@dataclass
class TimeoutConfig:
    connect: float = 10.0
    read: float = 30.0
    write: float = 10.0
    pool: float = 10.0
```

### RetryConfig
```python
@dataclass
class RetryConfig:
    max_retries: int = 3
    backoff_factor: float = 0.5
    backoff_max: float = 60.0
    retry_on_status: tuple = (429, 500, 502, 503, 504)
    retry_on_connection_error: bool = True
```

### LoggingConfig
```python
@dataclass
class LoggingConfig:
    level: Union[int, str] = logging.INFO
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    log_requests: bool = False
    log_responses: bool = False
    log_request_bodies: bool = False
    log_response_bodies: bool = False
```
