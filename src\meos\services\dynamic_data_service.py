"""
Dynamic data service for MeOS Python SDK.

This module provides dynamic property data operations.
Tag: 动态属性数据
"""

import logging
from typing import List, Optional

from ..models.dynamic_data import (
    DynamicHistoryRequest,
    DynamicHistoryResponse,
    DynamicRealtimeRequest,
    DynamicRealtimeResponse,
    DynamicRealtimeWithFormulaRequest,
    DynamicRealtimeWithFormulaResponse,
)
from .base_service import BaseService

logger = logging.getLogger(__name__)


class DynamicDataService(BaseService):
    """Dynamic data service for MeOS API - 动态属性数据."""

    def get_dynamic_history(
        self,
        data_codes: List[str],
        start_time: str,
        end_time: str,
    ) -> DynamicHistoryResponse:
        """
        获取动态属性历史数据.

        Args:
            data_codes: 属性编码列表
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        request_data = DynamicHistoryRequest(
            data_codes=data_codes,
            start_time=start_time,
            end_time=end_time,
        )
        json_data = self._validate_request_data(request_data)

        response = self._make_request(
            method="POST",
            url="/p/dt/v1/public/ins/dy/history",
            response_model=DynamicHistoryResponse,
            json_data=json_data,
        )

        return response

    async def get_dynamic_history_async(
        self,
        data_codes: List[str],
        start_time: str,
        end_time: str,
    ) -> DynamicHistoryResponse:
        """
        获取动态属性历史数据 (异步).

        Args:
            data_codes: 属性编码列表
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        request_data = DynamicHistoryRequest(
            data_codes=data_codes,
            start_time=start_time,
            end_time=end_time,
        )
        json_data = self._validate_request_data(request_data)

        response = await self._make_async_request(
            method="POST",
            url="/p/dt/v1/public/ins/dy/history",
            response_model=DynamicHistoryResponse,
            json_data=json_data,
        )

        return response

    def get_dynamic_realtime(
        self,
        data_codes: List[str],
        timestamp: Optional[str] = None,
    ) -> DynamicRealtimeResponse:
        """
        获取动态属性实时数据.

        Args:
            data_codes: 属性编码列表
            timestamp: 时间戳 (可选)

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        request_data = DynamicRealtimeRequest(
            data_codes=data_codes,
            timestamp=timestamp,
        )
        json_data = self._validate_request_data(request_data)

        response = self._make_request(
            method="POST",
            url="/p/dt/v1/public/ins/dy/rt",
            response_model=DynamicRealtimeResponse,
            json_data=json_data,
        )

        return response

    async def get_dynamic_realtime_async(
        self,
        data_codes: List[str],
        timestamp: Optional[str] = None,
    ) -> DynamicRealtimeResponse:
        """
        获取动态属性实时数据 (异步).

        Args:
            data_codes: 属性编码列表
            timestamp: 时间戳 (可选)

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        request_data = DynamicRealtimeRequest(
            data_codes=data_codes,
            timestamp=timestamp,
        )
        json_data = self._validate_request_data(request_data)

        response = await self._make_async_request(
            method="POST",
            url="/p/dt/v1/public/ins/dy/rt",
            response_model=DynamicRealtimeResponse,
            json_data=json_data,
        )

        return response

    def get_dynamic_realtime_with_formula(
        self,
        data_codes: List[str],
        timestamp: Optional[str] = None,
        include_formula: bool = True,
    ) -> DynamicRealtimeWithFormulaResponse:
        """
        获取动态属性实时数据（含公式计算）.

        Args:
            data_codes: 属性编码列表
            timestamp: 时间戳 (可选)
            include_formula: 是否包含公式计算

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        request_data = DynamicRealtimeWithFormulaRequest(
            data_codes=data_codes,
            timestamp=timestamp,
            include_formula=include_formula,
        )
        json_data = self._validate_request_data(request_data)

        response = self._make_request(
            method="POST",
            url="/p/dt/v1/public/ins/dy/rt/fm/calc",
            response_model=DynamicRealtimeWithFormulaResponse,
            json_data=json_data,
        )

        return response

    async def get_dynamic_realtime_with_formula_async(
        self,
        data_codes: List[str],
        timestamp: Optional[str] = None,
        include_formula: bool = True,
    ) -> DynamicRealtimeWithFormulaResponse:
        """
        获取动态属性实时数据（含公式计算）(异步).

        Args:
            data_codes: 属性编码列表
            timestamp: 时间戳 (可选)
            include_formula: 是否包含公式计算

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        request_data = DynamicRealtimeWithFormulaRequest(
            data_codes=data_codes,
            timestamp=timestamp,
            include_formula=include_formula,
        )
        json_data = self._validate_request_data(request_data)

        response = await self._make_async_request(
            method="POST",
            url="/p/dt/v1/public/ins/dy/rt/fm/calc",
            response_model=DynamicRealtimeWithFormulaResponse,
            json_data=json_data,
        )

        return response
