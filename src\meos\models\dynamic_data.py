"""
Dynamic data models for MeOS Python SDK.

This module contains models for dynamic data operations.
"""

from datetime import datetime
from typing import List, Optional, Union

from pydantic import Field, validator

from .base import BaseModel, BaseResponse
from .common import DynamicPropertyValue


class DynamicHistoryRequest(BaseModel):
    """Dynamic property history data request model."""
    
    data_codes: List[str] = Field(..., description="属性编码列表")
    start_time: Union[str, datetime] = Field(..., description="开始时间")
    end_time: Union[str, datetime] = Field(..., description="结束时间")
    
    @validator("start_time", "end_time", pre=True)
    def parse_datetime(cls, v):
        """Parse datetime from string if needed."""
        if isinstance(v, str):
            try:
                return datetime.fromisoformat(v.replace("Z", "+00:00"))
            except ValueError:
                # Try common format: "2024-01-01 00:00:00"
                return datetime.strptime(v, "%Y-%m-%d %H:%M:%S")
        return v
    
    @validator("end_time")
    def validate_time_range(cls, v, values):
        """Validate that end_time is after start_time."""
        if "start_time" in values and v <= values["start_time"]:
            raise ValueError("end_time must be after start_time")
        return v


class DynamicRealtimeRequest(BaseModel):
    """Dynamic property realtime data request model."""
    
    data_codes: List[str] = Field(..., description="属性编码列表")
    timestamp: Optional[Union[str, datetime]] = Field(None, description="时间戳")
    
    @validator("timestamp", pre=True)
    def parse_datetime(cls, v):
        """Parse datetime from string if needed."""
        if v is None:
            return v
        if isinstance(v, str):
            try:
                return datetime.fromisoformat(v.replace("Z", "+00:00"))
            except ValueError:
                return datetime.strptime(v, "%Y-%m-%d %H:%M:%S")
        return v


class DynamicRealtimeWithFormulaRequest(BaseModel):
    """Dynamic property realtime data with formula calculation request model."""
    
    data_codes: List[str] = Field(..., description="属性编码列表")
    timestamp: Optional[Union[str, datetime]] = Field(None, description="时间戳")
    include_formula: bool = Field(True, description="是否包含公式计算")
    
    @validator("timestamp", pre=True)
    def parse_datetime(cls, v):
        """Parse datetime from string if needed."""
        if v is None:
            return v
        if isinstance(v, str):
            try:
                return datetime.fromisoformat(v.replace("Z", "+00:00"))
            except ValueError:
                return datetime.strptime(v, "%Y-%m-%d %H:%M:%S")
        return v


class DynamicPropertyData(BaseModel):
    """Dynamic property data model."""
    
    data_code: str = Field(..., description="数据编码")
    value: Optional[Union[str, int, float, bool]] = Field(None, description="数据值")
    timestamp: Optional[datetime] = Field(None, description="时间戳")
    quality: Optional[str] = Field(None, description="数据质量")
    unit: Optional[str] = Field(None, description="单位")
    
    @property
    def is_good_quality(self) -> bool:
        """Check if the data quality is good."""
        return self.quality == "GOOD" if self.quality else True
    
    @property
    def is_numeric(self) -> bool:
        """Check if the value is numeric."""
        return isinstance(self.value, (int, float))
    
    @property
    def numeric_value(self) -> Optional[float]:
        """Get numeric value as float."""
        if self.is_numeric:
            return float(self.value)
        return None


class DynamicPropertyDataWithFormula(DynamicPropertyData):
    """Dynamic property data with formula calculation model."""
    
    formula_result: Optional[Union[str, int, float, bool]] = Field(None, description="公式计算结果")
    formula_expression: Optional[str] = Field(None, description="公式表达式")
    
    @property
    def has_formula(self) -> bool:
        """Check if this data has formula calculation."""
        return self.formula_expression is not None
    
    @property
    def formula_numeric_value(self) -> Optional[float]:
        """Get formula result as numeric value."""
        if isinstance(self.formula_result, (int, float)):
            return float(self.formula_result)
        return None


class DataAggregation(BaseModel):
    """Data aggregation model."""
    
    data_code: str = Field(..., description="数据编码")
    aggregation_type: str = Field(..., description="聚合类型")
    value: Optional[Union[str, int, float]] = Field(None, description="聚合值")
    count: int = Field(0, description="数据点数量")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    
    # Common aggregation types
    AVG = "avg"
    SUM = "sum"
    MIN = "min"
    MAX = "max"
    COUNT = "count"
    FIRST = "first"
    LAST = "last"


class DataQualityInfo(BaseModel):
    """Data quality information model."""
    
    data_code: str = Field(..., description="数据编码")
    total_points: int = Field(0, description="总数据点数")
    good_points: int = Field(0, description="良好数据点数")
    bad_points: int = Field(0, description="坏数据点数")
    missing_points: int = Field(0, description="缺失数据点数")
    quality_rate: float = Field(0.0, description="数据质量率")
    
    @property
    def is_high_quality(self) -> bool:
        """Check if data quality is high (>= 95%)."""
        return self.quality_rate >= 0.95


class DataStatistics(BaseModel):
    """Data statistics model."""
    
    data_code: str = Field(..., description="数据编码")
    count: int = Field(0, description="数据点数量")
    min_value: Optional[float] = Field(None, description="最小值")
    max_value: Optional[float] = Field(None, description="最大值")
    avg_value: Optional[float] = Field(None, description="平均值")
    sum_value: Optional[float] = Field(None, description="总和")
    std_dev: Optional[float] = Field(None, description="标准差")
    variance: Optional[float] = Field(None, description="方差")
    
    @property
    def range_value(self) -> Optional[float]:
        """Calculate range (max - min)."""
        if self.min_value is not None and self.max_value is not None:
            return self.max_value - self.min_value
        return None


# Response models
class DynamicHistoryResponse(BaseResponse[List[DynamicPropertyData]]):
    """Dynamic property history response model."""
    pass


class DynamicRealtimeResponse(BaseResponse[List[DynamicPropertyData]]):
    """Dynamic property realtime response model."""
    pass


class DynamicRealtimeWithFormulaResponse(BaseResponse[List[DynamicPropertyDataWithFormula]]):
    """Dynamic property realtime with formula response model."""
    pass


class DataAggregationResponse(BaseResponse[List[DataAggregation]]):
    """Data aggregation response model."""
    pass


class DataQualityResponse(BaseResponse[List[DataQualityInfo]]):
    """Data quality response model."""
    pass


class DataStatisticsResponse(BaseResponse[List[DataStatistics]]):
    """Data statistics response model."""
    pass
