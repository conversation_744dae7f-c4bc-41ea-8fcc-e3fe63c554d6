"""
Main client classes for MeOS Python SDK.

This module provides the primary client interfaces for interacting with the MeOS API.
"""

import logging
from typing import Any, Dict

from .base import BaseHTTPClient
from .config import ClientConfig
from .services import (
    AuthService,
    DynamicDataService,
    ProjectInstanceService,
)

logger = logging.getLogger(__name__)


class MeOSClient:
    """
    Synchronous client for MeOS API.

    This client provides access to all MeOS API endpoints organized by service categories.
    """

    def __init__(self, base_url: str, app_id: str, app_secret: str, **kwargs: Dict[Any, Any]) -> None:
        """
        Initialize the MeOS client.

        Args:
            base_url: Base URL for the MeOS API
            app_id: Application ID for authentication
            app_secret: Application secret for authentication
            **kwargs: Additional configuration options
        """
        # Create client configuration
        self.config = ClientConfig(base_url=base_url, app_id=app_id, app_secret=app_secret, **kwargs)

        # Initialize HTTP client
        self._http_client = BaseHTTPClient(self.config)

        # Initialize services
        self._auth = AuthService(self._http_client)
        self._dynamic_data = DynamicDataService(self._http_client)
        self._project_instances = ProjectInstanceService(self._http_client)

        logger.info(f"MeOS client initialized for {base_url}")

    @property
    def auth(self) -> AuthService:
        """Authentication service."""
        return self._auth

    @property
    def dynamic_data(self) -> DynamicDataService:
        """Dynamic data service - 动态属性数据."""
        return self._dynamic_data

    @property
    def project_instances(self) -> ProjectInstanceService:
        """Project instance service - 项目实例信息."""
        return self._project_instances

    def close(self) -> None:
        """Close the client and clean up resources."""
        self._http_client.close()
        logger.info("MeOS client closed")

    def __enter__(self) -> "MeOSClient":
        """Context manager entry."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        """Context manager exit."""
        self.close()

    def __repr__(self) -> str:
        return f"MeOSClient(base_url={self.config.base_url!r}, app_id={self.config.app_id!r})"


class AsyncMeOSClient:
    """
    Asynchronous client for MeOS API.

    This client provides async access to all MeOS API endpoints organized by service categories.
    """

    def __init__(self, base_url: str, app_id: str, app_secret: str, **kwargs) -> None:
        """
        Initialize the async MeOS client.

        Args:
            base_url: Base URL for the MeOS API
            app_id: Application ID for authentication
            app_secret: Application secret for authentication
            **kwargs: Additional configuration options
        """
        # Create client configuration
        self.config = ClientConfig(base_url=base_url, app_id=app_id, app_secret=app_secret, **kwargs)

        # Initialize HTTP client
        self._http_client = BaseHTTPClient(self.config)

        # Initialize services
        self._auth = AuthService(self._http_client)
        self._dynamic_data = DynamicDataService(self._http_client)
        self._project_instances = ProjectInstanceService(self._http_client)

        logger.info(f"Async MeOS client initialized for {base_url}")

    @property
    def auth(self) -> AuthService:
        """Authentication service."""
        return self._auth

    @property
    def dynamic_data(self) -> DynamicDataService:
        """Dynamic data service - 动态属性数据."""
        return self._dynamic_data

    @property
    def project_instances(self) -> ProjectInstanceService:
        """Project instance service - 项目实例信息."""
        return self._project_instances

    async def aclose(self) -> None:
        """Close the async client and clean up resources."""
        await self._http_client.aclose()
        logger.info("Async MeOS client closed")

    async def __aenter__(self) -> "AsyncMeOSClient":
        """Async context manager entry."""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb) -> None:
        """Async context manager exit."""
        await self.aclose()

    def __repr__(self) -> str:
        return f"AsyncMeOSClient(base_url={self.config.base_url!r}, app_id={self.config.app_id!r})"


def create_client(base_url: str, app_id: str, app_secret: str, async_client: bool = False, **kwargs) -> MeOSClient:
    """
    Factory function to create a MeOS client.

    Args:
        base_url: Base URL for the MeOS API
        app_id: Application ID for authentication
        app_secret: Application secret for authentication
        async_client: Whether to create an async client
        **kwargs: Additional configuration options

    Returns:
        MeOS client instance
    """
    if async_client:
        return AsyncMeOSClient(base_url, app_id, app_secret, **kwargs)
    else:
        return MeOSClient(base_url, app_id, app_secret, **kwargs)


def create_client_from_config(
    config: ClientConfig,
    async_client: bool = False,
) -> MeOSClient:
    """
    Create a MeOS client from a configuration object.

    Args:
        config: Client configuration
        async_client: Whether to create an async client

    Returns:
        MeOS client instance
    """
    if async_client:
        return AsyncMeOSClient(
            base_url=config.base_url,
            app_id=config.app_id,
            app_secret=config.app_secret,
            timeout=config.timeout,
            retry=config.retry,
            logging=config.logging,
            verify_ssl=config.verify_ssl,
            follow_redirects=config.follow_redirects,
            max_connections=config.max_connections,
            max_keepalive_connections=config.max_keepalive_connections,
            keepalive_expiry=config.keepalive_expiry,
            token_endpoint=config.token_endpoint,
            token_lifetime=config.token_lifetime,
            token_refresh_threshold=config.token_refresh_threshold,
            extra_headers=config.extra_headers,
        )
    else:
        return MeOSClient(
            base_url=config.base_url,
            app_id=config.app_id,
            app_secret=config.app_secret,
            timeout=config.timeout,
            retry=config.retry,
            logging=config.logging,
            verify_ssl=config.verify_ssl,
            follow_redirects=config.follow_redirects,
            max_connections=config.max_connections,
            max_keepalive_connections=config.max_keepalive_connections,
            keepalive_expiry=config.keepalive_expiry,
            token_endpoint=config.token_endpoint,
            token_lifetime=config.token_lifetime,
            token_refresh_threshold=config.token_refresh_threshold,
            extra_headers=config.extra_headers,
        )
