"""
Authentication models for MeOS Python SDK.

This module contains models for authentication-related API operations.
"""

from typing import Optional

from pydantic import Field

from .base import BaseModel, BaseResponse


class TokenRequest(BaseModel):
    """Token request model."""
    
    appId: str = Field(..., description="应用ID")
    appSecret: str = Field(..., description="应用密钥")


class TokenData(BaseModel):
    """Token data model."""
    
    token: str = Field(..., description="Siact-token")


class TokenResponse(BaseResponse[TokenData]):
    """Token response model."""
    
    data: Optional[TokenData] = Field(None, description="Token数据")


class AuthenticationInfo(BaseModel):
    """Authentication information model."""
    
    app_id: str = Field(..., description="应用ID")
    token: Optional[str] = Field(None, description="当前token")
    expires_at: Optional[float] = Field(None, description="token过期时间戳")
    is_authenticated: bool = Field(False, description="是否已认证")
    
    @property
    def has_valid_token(self) -> bool:
        """Check if there is a valid token."""
        return self.token is not None and self.is_authenticated
