"""
Production energy service for MeOS Python SDK.

This module provides production energy system operations.
Tag: 生产能耗信息
"""

import logging
from typing import Any, Dict, Optional

from ..models.base import BaseResponse
from ..models.common import PaginationRequest
from .base_service import BaseService

logger = logging.getLogger(__name__)


class ProductionEnergyService(BaseService):
    """Production energy service for MeOS API - 生产能耗信息."""

    def get_pcm_info(self, data_code: Optional[str] = None) -> BaseResponse:
        """
        获取生产能耗系统详细信息.

        Args:
            data_code: 数据编码

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=data_code)

        response = self._make_request(
            method="GET",
            url="/p/dt/v1/public/pcm/info",
            response_model=BaseResponse,
            params=params,
        )

        return response

    async def get_pcm_info_async(self, data_code: Optional[str] = None) -> BaseResponse:
        """
        获取生产能耗系统详细信息 (异步).

        Args:
            data_code: 数据编码

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=data_code)

        response = await self._make_async_request(
            method="GET",
            url="/p/dt/v1/public/pcm/info",
            response_model=BaseResponse,
            params=params,
        )

        return response

    def get_pcm_page(self, project_code: Optional[str] = None) -> BaseResponse:
        """
        获取项目下生产能耗系统.

        Args:
            project_code: 项目编码

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(projectCode=project_code)

        response = self._make_request(
            method="GET",
            url="/p/dt/v1/public/pcm/page",
            response_model=BaseResponse,
            params=params,
        )

        return response

    async def get_pcm_page_async(self, project_code: Optional[str] = None) -> BaseResponse:
        """
        获取项目下生产能耗系统 (异步).

        Args:
            project_code: 项目编码

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(projectCode=project_code)

        response = await self._make_async_request(
            method="GET",
            url="/p/dt/v1/public/pcm/page",
            response_model=BaseResponse,
            params=params,
        )

        return response

    def get_pcm_tree(self, data_code: str) -> BaseResponse:
        """
        查询生产能耗系统所有下级(树).

        Args:
            data_code: 数据编码

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=data_code)

        response = self._make_request(
            method="GET",
            url="/p/dt/v1/public/pcm/tree",
            response_model=BaseResponse,
            params=params,
        )

        return response

    async def get_pcm_tree_async(self, data_code: str) -> BaseResponse:
        """
        查询生产能耗系统所有下级(树) (异步).

        Args:
            data_code: 数据编码

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=data_code)

        response = await self._make_async_request(
            method="GET",
            url="/p/dt/v1/public/pcm/tree",
            response_model=BaseResponse,
            params=params,
        )

        return response

    def get_base_properties(
        self,
        pagination: PaginationRequest,
        data_code: Optional[str] = None,
    ) -> BaseResponse:
        """
        获取基础属性列表.

        Args:
            pagination: 分页参数
            data_code: 数据编码

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        request_data: Dict[str, Any] = {
            "current": pagination.page,
            "size": pagination.size,
        }
        if data_code:
            request_data["dataCode"] = data_code

        response = self._make_request(
            method="POST",
            url="/p/dt/v1/public/pcm/page/base",
            response_model=BaseResponse,
            json_data=request_data,
        )

        return response

    async def get_base_properties_async(
        self,
        pagination: PaginationRequest,
        data_code: Optional[str] = None,
    ) -> BaseResponse:
        """
        获取基础属性列表 (异步).

        Args:
            pagination: 分页参数
            data_code: 数据编码

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        request_data: Dict[str, Any] = {
            "current": pagination.page,
            "size": pagination.size,
        }
        if data_code:
            request_data["dataCode"] = data_code

        response = await self._make_async_request(
            method="POST",
            url="/p/dt/v1/public/pcm/page/base",
            response_model=BaseResponse,
            json_data=request_data,
        )

        return response

    def get_dynamic_properties(
        self,
        pagination: PaginationRequest,
        data_code: Optional[str] = None,
    ) -> BaseResponse:
        """
        获取动态属性列表.

        Args:
            pagination: 分页参数
            data_code: 数据编码

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        request_data: Dict[str, Any] = {
            "current": pagination.page,
            "size": pagination.size,
        }
        if data_code:
            request_data["dataCode"] = data_code

        response = self._make_request(
            method="POST",
            url="/p/dt/v1/public/pcm/page/dynamic",
            response_model=BaseResponse,
            json_data=request_data,
        )

        return response

    async def get_dynamic_properties_async(
        self,
        pagination: PaginationRequest,
        data_code: Optional[str] = None,
    ) -> BaseResponse:
        """
        获取动态属性列表 (异步).

        Args:
            pagination: 分页参数
            data_code: 数据编码

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        request_data: Dict[str, Any] = {
            "current": pagination.page,
            "size": pagination.size,
        }
        if data_code:
            request_data["dataCode"] = data_code

        response = await self._make_async_request(
            method="POST",
            url="/p/dt/v1/public/pcm/page/dynamic",
            response_model=BaseResponse,
            json_data=request_data,
        )

        return response

    def get_static_properties(
        self,
        pagination: PaginationRequest,
        data_code: Optional[str] = None,
    ) -> BaseResponse:
        """
        获取静态属性列表.

        Args:
            pagination: 分页参数
            data_code: 数据编码

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        request_data: Dict[str, Any] = {
            "current": pagination.page,
            "size": pagination.size,
        }
        if data_code:
            request_data["dataCode"] = data_code

        response = self._make_request(
            method="POST",
            url="/p/dt/v1/public/pcm/page/static",
            response_model=BaseResponse,
            json_data=request_data,
        )

        return response

    async def get_static_properties_async(
        self,
        pagination: PaginationRequest,
        data_code: Optional[str] = None,
    ) -> BaseResponse:
        """
        获取静态属性列表 (异步).

        Args:
            pagination: 分页参数
            data_code: 数据编码

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        request_data: Dict[str, Any] = {
            "current": pagination.page,
            "size": pagination.size,
        }
        if data_code:
            request_data["dataCode"] = data_code

        response = await self._make_async_request(
            method="POST",
            url="/p/dt/v1/public/pcm/page/static",
            response_model=BaseResponse,
            json_data=request_data,
        )

        return response
