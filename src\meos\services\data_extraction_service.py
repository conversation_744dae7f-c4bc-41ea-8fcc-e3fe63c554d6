"""
Data extraction service for MeOS Python SDK.

This module provides data extraction operations.
"""

import logging

from ..models.base import BaseResponse
from .base_service import BaseService

logger = logging.getLogger(__name__)


class DataExtractionService(BaseService):
    """Data extraction service for MeOS API - 数据提取."""

    def extract_device_data(
        self,
        project_code: str,
        interval: str,
        stime: str,
        etime: str,
        sys_code: str,
        station_code: str,
        unit_code: str,
        dev_code: str,
        current: int,
        size: int,
    ) -> BaseResponse:
        """
        单项目下任意设备数据获取.

        Args:
            project_code: 项目编码【数字孪生的长码】
            interval: 间隔时间，分钟级：1m  小时级：1h 日级：1d 月级：1n
            stime: 开始时间，格式："yyyy-MM-DD HH:mm:ss"
            etime: 结束时间，格式："yyyy-MM-DD HH:mm:ss"
            sys_code: 系统编码，数字孪生的长码
            station_code: 站编码，数字孪生的长码
            unit_code: 单元编码，数字孪生的长码
            dev_code: 设备编码，数字孪生的长码
            current: 当前页，从1开始
            size: 每页条数

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails

        Example:
            >>> data_service = DataExtractionService(http_client)
            >>> response = data_service.extract_device_data(
            ...     project_code="PGY02013_S0000000_ST00000000_U00000000_EQ000000000000_MP0000000",
            ...     interval="15m",
            ...     stime="2025-01-01 14:39:56",
            ...     etime="2025-02-28 14:39:56",
            ...     sys_code="PGY02013_SPD01002_ST00000000_U00000000_EQ000000000000_MP0000000",
            ...     station_code="",
            ...     unit_code="",
            ...     dev_code="",
            ...     current=1,
            ...     size=10
            ... )
        """
        request_data = {
            "projectCode": project_code,
            "interval": interval,
            "stime": stime,
            "etime": etime,
            "sysCode": sys_code,
            "stationCode": station_code,
            "unitCode": unit_code,
            "devCode": dev_code,
            "current": current,
            "size": size,
        }

        response = self._make_request(
            method="POST",
            url="/p/dw/data-extraction",
            response_model=BaseResponse,
            json_data=request_data,
        )

        return response

    # Async methods
    async def aextract_device_data(
        self,
        project_code: str,
        interval: str,
        stime: str,
        etime: str,
        sys_code: str,
        station_code: str,
        unit_code: str,
        dev_code: str,
        current: int,
        size: int,
    ) -> BaseResponse:
        """
        单项目下任意设备数据获取 (异步).

        Args:
            project_code: 项目编码【数字孪生的长码】
            interval: 间隔时间，分钟级：1m  小时级：1h 日级：1d 月级：1n
            stime: 开始时间，格式："yyyy-MM-DD HH:mm:ss"
            etime: 结束时间，格式："yyyy-MM-DD HH:mm:ss"
            sys_code: 系统编码，数字孪生的长码
            station_code: 站编码，数字孪生的长码
            unit_code: 单元编码，数字孪生的长码
            dev_code: 设备编码，数字孪生的长码
            current: 当前页，从1开始
            size: 每页条数

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        request_data = {
            "projectCode": project_code,
            "interval": interval,
            "stime": stime,
            "etime": etime,
            "sysCode": sys_code,
            "stationCode": station_code,
            "unitCode": unit_code,
            "devCode": dev_code,
            "current": current,
            "size": size,
        }

        response = await self._make_async_request(
            method="POST",
            url="/p/dw/data-extraction",
            response_model=BaseResponse,
            json_data=request_data,
        )

        return response
