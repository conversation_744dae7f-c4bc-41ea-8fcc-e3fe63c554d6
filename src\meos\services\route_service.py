"""
Route service for MeOS Python SDK.

This module provides route management operations.
"""

import logging
from typing import Any, Dict, List

from ..models.base import BaseResponse
from .base_service import BaseService

logger = logging.getLogger(__name__)


class RouteService(BaseService):
    """Route service for MeOS API - 路由管理."""

    def create_route(
        self,
        name: str,
        status: int,
        methods: List[str],
        uri: str,
        upstream: Dict[str, Any],
        desc: str = "",
    ) -> BaseResponse:
        """
        注册路由.

        Args:
            name: 路由名称
            status: 路由状态（0下线；1上线）
            methods: HTTP 方法列表，如 ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", "CONNECT", "TRACE", "PURGE"]
            uri: HTTP 请求路径，如 /foo/index.html，支持请求路径前缀 /foo/*。/* 代表所有路径
            upstream: 映射节点配置
            desc: 路由描述

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails

        Example:
            >>> route_service = RouteService(http_client)
            >>> upstream = {
            ...     "type": "roundrobin",
            ...     "pass_host": "pass",
            ...     "scheme": "http",
            ...     "timeout": {
            ...         "connect": 6,
            ...         "send": 6,
            ...         "read": 6
            ...     },
            ...     "nodes": {
            ...         "*************:80": 1
            ...     }
            ... }
            >>> response = route_service.create_route(
            ...     name="test-route",
            ...     status=1,
            ...     methods=["GET", "POST"],
            ...     uri="/test",
            ...     upstream=upstream
            ... )
        """
        request_data = {
            "name": name,
            "desc": desc,
            "status": status,
            "methods": methods,
            "uri": uri,
            "upstream": upstream,
        }

        response = self._make_request(
            method="POST",
            url="/p/sbus/admin/routes",
            response_model=BaseResponse,
            json_data=request_data,
        )

        return response

    def update_route(
        self,
        route_id: str,
        name: str,
        status: int,
        methods: List[str],
        uri: str,
        upstream: Dict[str, Any],
        desc: str = "",
    ) -> BaseResponse:
        """
        修改路由.

        Args:
            route_id: 路由ID
            name: 路由名称
            status: 路由状态（0下线；1上线）
            methods: HTTP 方法列表
            uri: HTTP 请求路径
            upstream: 映射节点配置
            desc: 路由描述

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        request_data = {
            "name": name,
            "desc": desc,
            "status": status,
            "methods": methods,
            "uri": uri,
            "upstream": upstream,
        }

        response = self._make_request(
            method="PUT",
            url=f"/p/sbus/admin/routes/{route_id}",
            response_model=BaseResponse,
            json_data=request_data,
        )

        return response

    def get_route(self, route_id: str) -> BaseResponse:
        """
        查询路由.

        Args:
            route_id: 路由ID

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        response = self._make_request(
            method="GET",
            url=f"/p/sbus/admin/routes/{route_id}",
            response_model=BaseResponse,
        )

        return response

    def update_route_status(self, route_id: str, status: int) -> BaseResponse:
        """
        路由上/下线.

        Args:
            route_id: 路由ID
            status: 路由状态（0 下线 1上线）

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        request_data = {"status": status}

        response = self._make_request(
            method="PATCH",
            url=f"/p/sbus/admin/routes/{route_id}",
            response_model=BaseResponse,
            json_data=request_data,
        )

        return response

    # Async methods
    async def acreate_route(
        self,
        name: str,
        status: int,
        methods: List[str],
        uri: str,
        upstream: Dict[str, Any],
        desc: str = "",
    ) -> BaseResponse:
        """
        注册路由 (异步).

        Args:
            name: 路由名称
            status: 路由状态（0下线；1上线）
            methods: HTTP 方法列表
            uri: HTTP 请求路径
            upstream: 映射节点配置
            desc: 路由描述

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        request_data = {
            "name": name,
            "desc": desc,
            "status": status,
            "methods": methods,
            "uri": uri,
            "upstream": upstream,
        }

        response = await self._amake_request(
            method="POST",
            url="/p/sbus/admin/routes",
            response_model=BaseResponse,
            json=request_data,
        )

        return response

    async def aupdate_route(
        self,
        route_id: str,
        name: str,
        status: int,
        methods: List[str],
        uri: str,
        upstream: Dict[str, Any],
        desc: str = "",
    ) -> BaseResponse:
        """
        修改路由 (异步).

        Args:
            route_id: 路由ID
            name: 路由名称
            status: 路由状态（0下线；1上线）
            methods: HTTP 方法列表
            uri: HTTP 请求路径
            upstream: 映射节点配置
            desc: 路由描述

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        request_data = {
            "name": name,
            "desc": desc,
            "status": status,
            "methods": methods,
            "uri": uri,
            "upstream": upstream,
        }

        response = await self._amake_request(
            method="PUT",
            url=f"/p/sbus/admin/routes/{route_id}",
            response_model=BaseResponse,
            json=request_data,
        )

        return response

    async def aget_route(self, route_id: str) -> BaseResponse:
        """
        查询路由 (异步).

        Args:
            route_id: 路由ID

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        response = await self._amake_request(
            method="GET",
            url=f"/p/sbus/admin/routes/{route_id}",
            response_model=BaseResponse,
        )

        return response

    async def aupdate_route_status(self, route_id: str, status: int) -> BaseResponse:
        """
        路由上/下线 (异步).

        Args:
            route_id: 路由ID
            status: 路由状态（0 下线 1上线）

        Returns:
            完整的API响应，包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        request_data = {"status": status}

        response = await self._amake_request(
            method="PATCH",
            url=f"/p/sbus/admin/routes/{route_id}",
            response_model=BaseResponse,
            json=request_data,
        )

        return response
