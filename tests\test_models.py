"""
Tests for MeOS model classes.
"""

import pytest
from datetime import datetime

from meos.models.base import BaseResponse, PageResponse, TreeNode, PropertyValue
from meos.models.auth import TokenRequest, TokenResponse, TokenData
from meos.models.instances import ProjectInstance
from meos.models.dynamic_data import DynamicPropertyData, DynamicHistoryRequest


class TestBaseModels:
    """Test cases for base model classes."""
    
    def test_base_response_success(self):
        """Test BaseResponse with success status."""
        response = BaseResponse[str](code=200, msg="success", data="test data")
        
        assert response.code == 200
        assert response.msg == "success"
        assert response.data == "test data"
        assert response.is_success is True
        assert response.is_error is False
    
    def test_base_response_error(self):
        """Test BaseResponse with error status."""
        response = BaseResponse[str](code=400, msg="error", data=None)
        
        assert response.code == 400
        assert response.msg == "error"
        assert response.data is None
        assert response.is_success is False
        assert response.is_error is True
    
    def test_page_response(self):
        """Test PageResponse model."""
        records = ["item1", "item2", "item3"]
        page_response = PageResponse[str](
            records=records,
            total=100,
            size=10,
            current=1,
            pages=10
        )
        
        assert page_response.records == records
        assert page_response.total == 100
        assert page_response.has_next is True
        assert page_response.has_previous is False
        
        page_info = page_response.page_info
        assert page_info.current == 1
        assert page_info.total == 100
    
    def test_tree_node(self):
        """Test TreeNode model."""
        root = TreeNode[str](
            id="root",
            name="Root Node",
            level=0,
            data="root data"
        )
        
        child1 = TreeNode[str](
            id="child1",
            name="Child 1",
            parent_id="root",
            level=1,
            data="child1 data"
        )
        
        child2 = TreeNode[str](
            id="child2",
            name="Child 2",
            parent_id="root",
            level=1,
            data="child2 data"
        )
        
        root.children = [child1, child2]
        
        assert root.is_root is True
        assert root.is_leaf is False
        assert root.has_children is True
        assert len(root.children) == 2
        
        assert child1.is_root is False
        assert child1.is_leaf is True
        
        # Test finding child
        found_child = root.find_child("child1")
        assert found_child == child1
        
        # Test finding descendant
        found_descendant = root.find_descendant("child2")
        assert found_descendant == child2
        
        # Test getting all descendants
        descendants = root.get_all_descendants()
        assert len(descendants) == 2
        assert child1 in descendants
        assert child2 in descendants
    
    def test_property_value(self):
        """Test PropertyValue model."""
        prop = PropertyValue(
            property_code="TEMP_001",
            property_name="Temperature",
            value=25.5,
            unit="°C",
            data_type="float",
            timestamp=datetime.now(),
            quality="GOOD"
        )
        
        assert prop.property_code == "TEMP_001"
        assert prop.is_numeric is True
        assert prop.is_boolean is False
        assert prop.is_string is False
        assert prop.numeric_value == 25.5
        
        # Test string value
        string_prop = PropertyValue(
            property_code="STATUS_001",
            value="RUNNING"
        )
        assert string_prop.is_string is True
        assert string_prop.is_numeric is False
        
        # Test boolean value
        bool_prop = PropertyValue(
            property_code="ENABLED_001",
            value=True
        )
        assert bool_prop.is_boolean is True


class TestAuthModels:
    """Test cases for authentication models."""
    
    def test_token_request(self):
        """Test TokenRequest model."""
        request = TokenRequest(appId="test_app", appSecret="test_secret")
        
        assert request.appId == "test_app"
        assert request.appSecret == "test_secret"
    
    def test_token_data(self):
        """Test TokenData model."""
        token_data = TokenData(token="test_token_123")
        
        assert token_data.token == "test_token_123"
    
    def test_token_response(self):
        """Test TokenResponse model."""
        token_data = TokenData(token="test_token_123")
        response = TokenResponse(code=200, msg="success", data=token_data)
        
        assert response.is_success is True
        assert response.data.token == "test_token_123"


class TestInstanceModels:
    """Test cases for instance models."""
    
    def test_project_instance(self):
        """Test ProjectInstance model."""
        project = ProjectInstance(
            data_code="PROJECT_001",
            name="Test Project",
            pro_name="Test Project",
            pro_type="Manufacturing",
            description="Test project description"
        )
        
        assert project.data_code == "PROJECT_001"
        assert project.name == "Test Project"
        assert project.pro_name == "Test Project"
        assert project.pro_type == "Manufacturing"


class TestDynamicDataModels:
    """Test cases for dynamic data models."""
    
    def test_dynamic_property_data(self):
        """Test DynamicPropertyData model."""
        now = datetime.now()
        data = DynamicPropertyData(
            data_code="TEMP_001",
            value=25.5,
            timestamp=now,
            quality="GOOD",
            unit="°C"
        )
        
        assert data.data_code == "TEMP_001"
        assert data.value == 25.5
        assert data.timestamp == now
        assert data.is_good_quality is True
        assert data.is_numeric is True
        assert data.numeric_value == 25.5
    
    def test_dynamic_property_data_bad_quality(self):
        """Test DynamicPropertyData with bad quality."""
        data = DynamicPropertyData(
            data_code="TEMP_001",
            value=25.5,
            quality="BAD"
        )
        
        assert data.is_good_quality is False
    
    def test_dynamic_history_request(self):
        """Test DynamicHistoryRequest model."""
        request = DynamicHistoryRequest(
            data_codes=["TEMP_001", "PRESSURE_001"],
            start_time="2024-01-01 00:00:00",
            end_time="2024-01-01 01:00:00"
        )
        
        assert len(request.data_codes) == 2
        assert "TEMP_001" in request.data_codes
        assert isinstance(request.start_time, datetime)
        assert isinstance(request.end_time, datetime)
    
    def test_dynamic_history_request_validation(self):
        """Test DynamicHistoryRequest validation."""
        with pytest.raises(ValueError):
            # end_time before start_time should raise error
            DynamicHistoryRequest(
                data_codes=["TEMP_001"],
                start_time="2024-01-01 01:00:00",
                end_time="2024-01-01 00:00:00"  # Before start_time
            )


class TestModelSerialization:
    """Test cases for model serialization."""
    
    def test_model_dump(self):
        """Test model serialization."""
        project = ProjectInstance(
            data_code="PROJECT_001",
            name="Test Project",
            pro_name="Test Project",
            pro_type="Manufacturing"
        )
        
        data = project.model_dump()
        assert isinstance(data, dict)
        assert data["data_code"] == "PROJECT_001"
        assert data["pro_name"] == "Test Project"
    
    def test_model_dump_by_alias(self):
        """Test model serialization by alias."""
        project = ProjectInstance(
            data_code="PROJECT_001",
            name="Test Project",
            pro_name="Test Project",
            pro_type="Manufacturing"
        )
        
        data = project.model_dump(by_alias=True)
        assert isinstance(data, dict)
        # Should use alias if defined
        assert "data_code" in data
    
    def test_model_validation(self):
        """Test model validation from dict."""
        data = {
            "data_code": "PROJECT_001",
            "name": "Test Project",
            "pro_name": "Test Project",
            "pro_type": "Manufacturing"
        }
        
        project = ProjectInstance.model_validate(data)
        assert project.data_code == "PROJECT_001"
        assert project.pro_name == "Test Project"


if __name__ == "__main__":
    pytest.main([__file__])
